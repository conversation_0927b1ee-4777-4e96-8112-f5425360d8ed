<script setup lang="tsx">
import type { ProTableColumns } from '@pubinfo/pro-components';
import { ProTable } from '@pubinfo/pro-components';
import Tree from '../../components/DataSectionDomainTree.vue';
import AddDrawer from './components/add-drawer.vue';

const columns = ref<ProTableColumns>([
  {
    hideInSearch: true,
    title: '序号',
    customRender: ({ index }: { index: number }) => {
      return (<span>{index + 1}</span>);
    },
  },
  {
    hideInSearch: true,
    title: '接口名称',
    dataIndex: 'name',
  },
  {
    hideInSearch: true,
    title: '说明',
    dataIndex: 'summary',
  },
  {
    hideInSearch: true,
    title: '发布日期',
    dataIndex: 'publishTime',
  },
  {
    hideInSearch: true,
    title: '来源系统',
    dataIndex: 'fromSystem',
  },
  {
    hideInSearch: true,
    title: '授权数量',
    dataIndex: 'authNum',
  },
  {
    hideInSearch: true,
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
]);

const tableRef = ref();

const currentNode = ref<any>({});
function onSelect(node: { nodeType: string, nodeId: string }) {
  currentNode.value = {
    nodeId: node.nodeId,
    nodeType: node.nodeType,
  };
  tableRef.value.reload();
}

async function request(params: any) {
  if (!currentNode.value.nodeId) {
    return;
  }
  const { current, pageSize, ...rest } = params;
  const res = await postApiUserGetUserApiByProjectDomain({
    currentPage: current,
    pageSize,
    projectid: currentNode.value.nodeType === 'section' ? Number(currentNode.value.nodeId) : undefined,
    domainId: currentNode.value.nodeType === 'domain' ? Number(currentNode.value.nodeId) : undefined,
    ...rest,
  });
  return {
    success: res.success,
    data: res.data?.content ?? [],
    total: res.data?.totalElements ?? 0,
  };
}

const addDrawer = ref();
function handleManage(record) {
  addDrawer.value.open(record);
}

// 刷新数据
function updateData() {
  tableRef.value.fetch();
}
</script>

<template>
  <div class="w-full h-full py-29px px-40px flex flex-col">
    <div class="h-[68px] py-24px px-30px bg-[rgba(255,255,255,0.6)] rounded-t-[6px]">
      <div class="text-[20px] text-[#161E5D] font-semibold leading-[23px] mb-[20px]">
        接口权限
      </div>
    </div>
    <div class="flex-auto bg-[#FFF] p-[30px]">
      <a-row :gutter="[8, 8]" :wrap="false" class="h-full">
        <a-col flex="320px" class="h-full overflow-hidden">
          <Tree @select="onSelect" />
        </a-col>
        <a-col class="h-full w-full overflow-hidden">
          <ProTable
            ref="tableRef" row-key="id" :request="request" :columns="columns" :scroll="{ x: true }"
            :toolbar="false" :search="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a class="flex items-center" @click="handleManage(record)">
                    <img class="w-16px h-16px" src="@/assets/images/data-center/api-data/auth.png" alt="">
                    <span class="ml-4px">授权管理</span>
                  </a>
                </a-space>
              </template>
            </template>
          </ProTable>
        </a-col>
      </a-row>
    </div>
    <AddDrawer ref="addDrawer" @submit="updateData" />
  </div>
</template>

<style scoped lang="scss">
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  overflow: hidden;
}

.status-filter {
  padding: 8px 0;
  font-size: 14px;
  font-weight: 400;
  color: #161e5d;
}

.button-group {
  padding-top: 8px;
}
</style>
