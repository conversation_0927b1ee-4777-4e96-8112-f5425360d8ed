<script setup lang="tsx">
import type { ProColumns } from '@pubinfo/pro-components';
import { ProTable } from '@pubinfo/pro-components';
import { useToggle } from '@vueuse/core';
import { Badge, message } from 'ant-design-vue';
import CustomDrawer from '@/components/CustomDrawer/index.vue';

defineOptions({
  name: 'AddDrawer',
});

// const emit = defineEmits(['submit']);
const [open, setOpen] = useToggle(false);
const [loading, setLoading] = useToggle(false);

const title = ref('账号授权');
const apiId = ref();

const typeOptions: Ref<{ label?: string, value?: string }[] | undefined> = ref([]);
function getRoleType() {
  getDictItemList({ dictCode: 'roleType' }).then((res: API.ResponseDataListDictItemVo) => {
    typeOptions.value = res.data?.map((e) => {
      return {
        label: e.dictLabel,
        value: e.dictValue,
      };
    });
  });
}

const selectedRowKeys = ref<any>([]);
function onSelectChange(Keys: any[]) {
  selectedRowKeys.value = Keys;
}

async function onOpen(record: any) {
  if (record && record.id) {
    apiId.value = record.id;
  }
  selectedRowKeys.value = [];
  getRoleType();
  setOpen(true);
}

const tableRef = ref();
const columns: ProColumns<any> = [
  {
    hideInSearch: true,
    title: '序号',
    customRender: ({ index }: { index: number }) => {
      return (<span>{index + 1}</span>);
    },
  },
  {
    title: '账号',
    dataIndex: 'name',
    hideInSearch: true,
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    hideInSearch: true,
  },
  {
    title: '手机',
    dataIndex: 'phone',
    hideInSearch: true,
  },
  {
    title: '角色',
    dataIndex: 'role',
    hideInSearch: true,
  },
  {
    title: '状态',
    hideInSearch: true,
    dataIndex: 'isAuth',
    customRender: ({ text }: { text: number }) => {
      return (
        text === 0
          ? <Badge status="success" text="已授权" />
          : <Badge status="warning" text="未授权" />
      );
    },
  },
  {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    hideInSearch: true,
  },
];

const searchParams = reactive({
  name: '',
  authorization: undefined,
});

async function request(params: any) {
  const { current, pageSize, ...rest } = params;
  const res = await postApiUserGetUserIsAuthorization({
    ...searchParams,
    apiId: apiId.value,
    ...rest,
  });
  const tableData = res.data ?? [];
  const total = res.data.length ?? 0;
  return {
    success: true,
    data: tableData,
    total,
  };
}

const dataOptions = ref<any>([
  { label: '已授权', value: 0 },
  { label: '未授权', value: 1 },
]);

// 刷新数据
function updateData() {
  tableRef.value.fetch();
}

function onAction(key: string, record?: any) {
  switch (key) {
    case 'addSingle':
      postApiUserAddAuthorizationUser({
        userId: [record.userId],
        apiId: apiId.value,
      }).then((res) => {
        message.success('授权成功');
        updateData();
      });
      break;
    case 'add':
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要授权的用户');
      }
      else {
        postApiUserAddAuthorizationUser({
          userId: selectedRowKeys.value,
          apiId: apiId.value,
        }).then((res) => {
          message.success('授权成功');
          updateData();
        });
      }
      break;
    case 'delSingle':
      postApiUserRevokeUser({
        userId: [record.userId],
        apiId: apiId.value,
      }).then((res) => {
        message.success('取消授权成功');
        updateData();
      });
      break;
    case 'del':
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要取消授权的用户');
      }
      else {
        postApiUserRevokeUser({
          userId: selectedRowKeys.value,
          apiId: apiId.value,
        }).then((res) => {
          message.success('取消授权成功');
          updateData();
        });
      }
      break;
    default:
      break;
  }
}

function close() {
  setOpen(false);
}

defineExpose({
  open: onOpen,
});
</script>

<template>
  <CustomDrawer
    v-model:open="open" :title="title" placement="right" :width="630" destroy-on-close
    :footer-style="{ textAlign: 'right' }" @close="close"
  >
    <div class="flex items-center mb-[20px] w-[100%]">
      <a-input-search
        v-model:value="searchParams.name" enter-button placeholder="请输入内容" class="w-200px mr-[16px]"
        @search="updateData"
      />
      <div class="mr-10px">
        状态：
        <a-select
          v-model:value="searchParams.authorization" class="w-200px" placeholder="请选择" :allow-clear="true"
          @change="updateData"
        >
          <a-select-option v-for="item in dataOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </div>
    <div class="mb-20px flex">
      <a-button type="primary" ghost class="flex items-center" @click="onAction('add')">
        <img class="w-16px h-16px mr-4px" src="@/assets/images/data-center/api-data/auth.png" alt="">
        批量授权
      </a-button>
      <a-button type="primary" danger ghost class="flex items-center ml-20px" @click="onAction('del')">
        <MinusCircleOutlined />
        批量取消
      </a-button>
    </div>
    <ProTable
      ref="tableRef" row-key="userId" :request="request" :columns="columns" :toolbar="false" :search="false"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }" :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'role'">
          <div> {{ typeOptions.find(item => item.value === record.role)?.label }} </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <div
              v-if="record.isAuth === 0" class="cursor-pointer flex items-center text-[red]"
              @click="onAction('delSingle', record)"
            >
              <img class="w-14px h-14px" src="@/assets/images/data-center/api-data/del.png" alt="">
              <span class="ml-4px">取消授权</span>
            </div>
            <a v-else class="cursor-pointer flex items-center" @click="onAction('addSingle', record)">
              <PlusCircleOutlined />
              <span class="ml-4px">授权</span>
            </a>
          </a-space>
        </template>
      </template>
    </ProTable>
    <template #footer>
      <a-button @click="close">
        取消
      </a-button>
      <a-button type="primary" class="ml-2" :loading="loading" @click="close">
        确定
      </a-button>
    </template>
  </CustomDrawer>
</template>
