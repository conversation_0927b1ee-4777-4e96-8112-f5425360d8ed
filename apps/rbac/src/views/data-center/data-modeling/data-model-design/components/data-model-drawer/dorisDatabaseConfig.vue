<script setup lang="tsx">
import type { ProColumns, ProTableProps } from '@pubinfo/pro-components';
import type { SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import type { IndexDataItem, TableFieldItem } from '../../types';
import eventBus from '@/utils/eventBus';
import IndexConfig from './indexConfig.vue';

const props = defineProps<{
  initState: API.DataWarehouseModel | null
  typeOptions: SelectProps['options']
  isEdit: boolean
}>();

const importStandardFieldRef = ref();
const indexConfigRef = ref();
const tableRef = ref();
const formRef = ref();

const isEdit = computed(() => props.isEdit);

type EditableData = Record<string, TableFieldItem>;
const editableData: EditableData = reactive({});

const fieldList = computed<TableFieldItem[]>(() => {
  return Object.values(editableData);
});

const indexConfigList = ref<IndexDataItem[]>([]);

const typeOptions = computed(() => props.typeOptions);

type tempKey = string | number;
const rowSelection = computed<ProTableProps['rowSelection']>(() => ({
  onChange: (selectedRowKeys: tempKey[], selectedRows: TableFieldItem[]) => {
    // console.log(`selectedRowKeys: ${selectedRowKeys}`, typeof selectedRowKeys);
  },
  // getCheckboxProps: (record: DataType) => ({
  //   disabled: record.name === 'Disabled User', // Column configuration not to be checked
  //   name: record.name,
  // }),
}));

const rules = computed(() => {
  const formRules: Record<string, Record<string, Rule[]>> = {};

  fieldList.value.forEach((item) => {
    formRules[item.id!] = {
      name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      fieldName: [{ required: true, message: '请输入字段名', trigger: 'blur' }],
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    };
  });

  return formRules;
});

const columns: ProColumns<TableFieldItem> = [
  {
    width: 80,
    title: '序号',
    hideInSearch: true,
    dataIndex: 'index',
    fixed: 'left',
  },
  {
    width: 200,
    title: '名称',
    hideInSearch: true,
    dataIndex: 'name',
  },
  {
    width: 200,
    title: '字段名',
    hideInSearch: true,
    dataIndex: 'fieldName',
  },
  {
    width: 200,
    title: '类型',
    hideInSearch: true,
    dataIndex: 'type',
  },
  {
    width: 200,
    title: '数据库类型',
    hideInSearch: true,
    dataIndex: 'dbType',
  },
  {
    width: 200,
    title: '字段长度',
    hideInSearch: true,
    dataIndex: 'length',
  },
  {
    width: 200,
    title: '小数点位数',
    hideInSearch: true,
    dataIndex: 'decLength',
  },
  {
    width: 200,
    title: '分桶',
    hideInSearch: true,
    dataIndex: 'bucket',
  },
  {
    width: 200,
    title: '主键',
    hideInSearch: true,
    dataIndex: 'isPrimaryKey',
  },
  {
    width: 200,
    title: '非空',
    hideInSearch: true,
    dataIndex: 'isNotEmpty',
  },
  {
    width: 200,
    title: '自增',
    hideInSearch: true,
    dataIndex: 'isAutoInc',
  },
  {
    width: 200,
    title: '是否分区',
    hideInSearch: true,
    dataIndex: 'isPartition',
  },
  {
    width: 200,
    title: '默认值',
    hideInSearch: true,
    dataIndex: 'defaultVal',
  },
  {
    width: 200,
    title: '字段说明',
    hideInSearch: true,
    dataIndex: 'comment',
  },
  {
    width: 180,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
];

function deleteData(key: string) {
  if (editableData[key]) {
    indexConfigList.value.forEach((item) => {
      item.fields = item.fields.filter(field => field !== editableData[key].fieldName);
    });
    delete editableData[key];
  }
}

function batchDeleteData() {
  // console.log('editableData', editableData);
  // validate();
}

async function createIndex() {
  const res = await validate();

  indexConfigRef.value.open({
    fieldList: fieldList.value,
    initIndexDataSource: indexConfigList.value,
    isDorisFlag: true,
  });
}

function getUUID() {
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 10000);
  return `custom-${timestamp}-${random.toString().padStart(4, '0')}`;
}

function addData() {
  const newItem = {
    id: getUUID(),
    name: '',
    fieldName: '',
    type: '',
    dbType: '',
    length: 0,
    decLength: 0,
    bucket: 0,
    isPrimaryKey: false,
    isNotEmpty: false,
    isAutoInc: false,
    isPartition: false,
    defaultVal: '',
    comment: '',
  };
  editableData[newItem.id] = newItem;
}

function importField() {
  importStandardFieldRef.value.open();
}

function handleIndexConfigSubmit(indexDataSource: IndexDataItem[]) {
  indexConfigList.value = indexDataSource;
}

// async function validate() {
//   try {
//     await formRef.value?.validate();
//     return true;
//   }
//   catch (error) {
//     return false;
//   }
// }

async function validate(needValidate = true) {
  if (needValidate) {
    await formRef.value?.validate();
  }
  return {
    indexData: indexConfigList.value,
    dataSource: fieldList.value,
  };
}

defineExpose({
  validate,
});

function init() {
  if (props.initState) {
    const { columns = [], indexs = [] } = props.initState;

    columns.forEach((item) => {
      editableData[item.id!] = {
        id: String(item.id!),
        name: item.name,
        fieldName: item.colname,
        type: item.colFlinkType,
        dbType: item.coltype,
        length: item.length,
        decLength: item.decLength,
        bucket: item.buckets,
        isPrimaryKey: item.pk === 1,
        isNotEmpty: item.notNull === 1,
        isAutoInc: item.autoinc === 1,
        isPartition: item.partition === 1,
        defaultVal: item.defaultValue,
        comment: item.comment,
      };
    });

    indexConfigList.value = indexs.map((item) => {
      return {
        id: item.id!,
        indexName: item.name!,
        indexType: item.type,
        fields: item.columnNames.split(','),
      };
    });
  }
}

// 数据源信息
const dataSourceInfo = ref({
  sourceType: undefined as string | undefined,
});

// 根据类型和数据源更新 dbType
function updateDbType(record: TableFieldItem) {
  if (!record.type) {
    return;
  }

  const typeOption = typeOptions.value?.find(option => option.value === record.type);
  if (!typeOption) {
    return;
  }

  // 使用策略模式根据数据源类型选择对应的数据库类型
  const dbTypeMapping = {
    // MySQL: () => typeOption.mysqlType,
    // PostgreSQL: () => typeOption.pgsqlType,
    // Hive: () => typeOption.hiveType,
    // Hudi: () => typeOption.hudiType,
    Doris: () => typeOption.dorisType,
  };

  const getDbType = dbTypeMapping[dataSourceInfo.value.sourceType as keyof typeof dbTypeMapping];
  record.dbType = getDbType ? getDbType() : '';
}

// 监听数据源变化事件
onMounted(() => {
  init();
  eventBus.on('data-source-change', (data) => {
    dataSourceInfo.value = data;

    // 更新所有记录的 dbType
    Object.values(editableData).forEach((record) => {
      if (record.type) {
        updateDbType(record);
      }
    });
  });
});

onUnmounted(() => {
  eventBus.off('data-source-change');
});
</script>

<template>
  <div>
    <a-space class="mt-[31px] mb-[24px]">
      <!-- <a-button type="primary" @click="importField">
        <template #icon>
          <LinkOutlined />
        </template>
        引用数据元
      </a-button>
      <a-button type="primary" ghost>
        <template #icon>
          <ImportOutlined />
        </template>
        导入模板
      </a-button>
      <a-button type="primary" ghost>
        <template #icon>
          <ExportOutlined />
        </template>
        批量导出
      </a-button> -->
      <!-- <a-button type="primary" danger ghost @click="batchDeleteData">
        <template #icon>
          <DeleteOutlined />
        </template>
        批量删除
      </a-button> -->
      <a-button type="primary" @click="createIndex">
        <template #icon>
          <ExportOutlined />
        </template>
        索引
      </a-button>
    </a-space>
    <a-form
      ref="formRef"
      :model="editableData"
      :rules="rules"
    >
      <a-table
        ref="tableRef"
        row-key="id"
        :data-source="fieldList"
        :columns="columns"
        :toolbar="false"
        :search="false"
        :pagination="false"
        :scroll="{ x: 1500 }"
      >
        <template #headerCell="{ column }">
          <template v-if="['name', 'fieldName', 'type'].includes(column.dataIndex as 'name' | 'fieldName' | 'type')">
            <span class="text-red-500 font-[SimSun,sans-serif]">*</span>
            <span>{{ column.title }}</span>
          </template>
          <template v-else>
            {{ column.title }}
          </template>
        </template>
        <template #bodyCell="{ column, index, record }">
          <template v-if="['name', 'fieldName', 'defaultVal', 'comment'].includes(column.dataIndex as 'name' | 'fieldName' | 'defaultVal' | 'comment')">
            <a-form-item :name="[record.id, column.dataIndex]">
              <a-input
                v-model:value="editableData[record.id][column.dataIndex as 'name' | 'fieldName']"
                :disabled="['fieldName', 'defaultVal'].includes(column.dataIndex as 'fieldName' | 'defaultVal') && !record.id?.includes('custom')"
              />
            </a-form-item>
          </template>
          <template v-if="['length', 'decLength', 'bucket'].includes(column.dataIndex as 'length' | 'decLength')">
            <a-form-item :name="[record.id, column.dataIndex]">
              <a-input-number
                v-model:value="editableData[record.id][column.dataIndex as 'length' | 'decLength']"
                :disabled="!record.id?.includes('custom')"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'type'">
            <a-form-item :name="[record.id, 'type']">
              <a-select
                v-model:value="editableData[record.id].type"
                class="w-full"
                :disabled="!record.id?.includes('custom')"
                :options="typeOptions"
                @change="() => updateDbType(editableData[record.id])"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'index'">
            <a-form-item :name="[record.id, column.dataIndex]">
              {{ index + 1 }}
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'dbType'">
            <a-form-item :name="[record.id, column.dataIndex]">
              {{ record.dbType || '-' }}
            </a-form-item>
          </template>
          <template v-if="['isPrimaryKey', 'isNotEmpty', 'isPartition', 'isAutoInc'].includes(column.dataIndex as 'isPrimaryKey' | 'isNotEmpty' | 'isPartition')">
            <a-form-item :name="[record.id, column.dataIndex]">
              <a-checkbox
                v-model:checked="editableData[record.id][column.dataIndex as 'isPrimaryKey' | 'isNotEmpty' | 'isPartition']"
                :disabled="!record.id?.includes('custom')"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="primary" danger :disabled="!record.id?.includes('custom')" @click="deleteData(record.id)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
        <template #footer>
          <div class="flex items-center justify-center text-[#2469F1] cursor-pointer" @click="addData">
            <PlusOutlined />  新增字段
          </div>
        </template>
      </a-table>
    </a-form>
    <!-- <ImportStandardField ref="importStandardFieldRef" /> -->
    <IndexConfig ref="indexConfigRef" @submit="handleIndexConfigSubmit" />
  </div>
</template>

<!-- <style lang="scss" scoped>

</style> -->
