<script setup lang="tsx">
import type { DataModelItem, FormState, IndexDataItem, TableFieldItem } from '../../types';
import { useToggle } from '@vueuse/core';
import { message } from 'ant-design-vue';
import CustomDrawer from '@/components/CustomDrawer/index.vue';
import First from './first.vue';
import Second from './second.vue';

defineOptions({
  name: 'DataModelDrawer',
});

const emit = defineEmits(['save']);

const steps = [
  { title: '第一步', key: 'first', subTitle: '编辑任务信息', icon: new URL('@/assets/images/data-center/data-modeling/icon-data-model-step1.png', import.meta.url).href },
  { title: '第二步', key: 'second', subTitle: '数据库配置', icon: new URL('@/assets/images/data-center/data-modeling/icon-data-model-step2.png', import.meta.url).href },
  // { title: '第三步', key: 'third', subTitle: '映射转换配置', icon: new URL('@/assets/images/data-center/data-modeling/icon-data-model-step3.png', import.meta.url).href },
];

const isEdit = ref(false);

const firstRef = ref<InstanceType<typeof First>>();
const secondRef = ref<InstanceType<typeof Second>>();
const initState = ref<API.DataWarehouseModel | null>(null);
const modelId = ref<number | null>(null);

const currentStep = ref<number>(0);

const [open, setOpen] = useToggle(false);

async function onOpen(data?: DataModelItem) {
  isEdit.value = false;

  if (data) {
    const res = await postDataWarehouseModelDetail({
      id: data.id!,
    });
    if (res.success) {
      initState.value = res.data || {};
      modelId.value = data.id!;

      isEdit.value = res.data?.status === 1;
    }
  }
  else {
    initState.value = null;
    modelId.value = null;
  }

  currentStep.value = 0;
  setOpen(true);
}

function close() {
  // form.value = {
  //   enable: true,
  // };
  setOpen(false);
}

function last() {
  currentStep.value--;
}

function next() {
  currentStep.value++;
}

async function save(isNeedValidate = true) {
// 校验所有表单
  const validations = [
    { step: 0, ref: firstRef, name: '对象信息' },
    { step: 1, ref: secondRef, name: '属性信息' },
  ];

  interface secondRes {
    dataSource: TableFieldItem[]
    indexData: IndexDataItem[]
  }

  const resList: [FormState, secondRes] = [] as unknown as [FormState, secondRes];
  for (const validation of validations) {
    try {
      const temp = await validation.ref.value?.validate(isNeedValidate);
      if (temp) {
        resList.push(temp);
      }
    }
    catch (error) {
      // 切换到对应步骤
      currentStep.value = validation.step;
      return;
    }
  }

  const tempColumns = resList[1].dataSource?.map((item) => {
    return {
      id: item.id?.toString().includes('custom') ? 0 : Number(item.id),
      modelid: modelId.value ? modelId.value : undefined,
      name: item.name,
      colname: item.fieldName,
      colFlinkType: item.type,
      coltype: item.dbType,
      length: item.length,
      decLength: item.decLength,
      comment: item.comment,
      pk: item.isPrimaryKey ? 1 : 0,
      autoinc: item.isAutoInc ? 1 : 0,
      notNull: item.isNotEmpty ? 1 : 0,
      partition: item.isPartition ? 1 : 0,
      precombine: item.isUnique ? 1 : 0,
      buckets: item.bucket,
      defaultValue: item.defaultVal,
    };
  }) || [];

  const tempIndexData = resList[1].indexData?.map((item) => {
    return {
      id: item.id?.toString().includes('custom') ? 0 : Number(item.id),
      modelid: modelId.value ? modelId.value : undefined,
      name: item.indexName,
      columnNames: item.fields.join(','),
      type: item.indexType,
    };
  }) || [];

  const fetchApi = modelId.value ? postDataWarehouseModelUpdate : postDataWarehouseModelAdd;

  const res = await fetchApi({
    id: modelId.value ? modelId.value : undefined,
    layerid: resList[0].dwLayer,
    dbSourceId: resList[0].dataSource,
    modelName: resList[0].modelName,
    tablePrefix: resList[0].prefix,
    tableNameTemp: resList[0].newTableName,
    tableName: resList[0].modelTableName,
    createType: resList[0].modelMode,
    status: isNeedValidate ? 1 : 2,
    remark: resList[0].remark,
    columns: tempColumns,
    indexs: tempIndexData,
  });

  if (res.success) {
    message.success('保存成功');
    emit('save');
    close();
  }
}

function saveDraft() {
  const isNeedValidate = false;
  save(isNeedValidate);
}

function onSubmit() {
  const isNeedValidate = true;
  save(isNeedValidate);
}

defineExpose({
  open: onOpen,
});
</script>

<template>
  <CustomDrawer
    v-model:open="open"
    title="数据建模"
    placement="right"
    :width="1000"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    @close="close"
  >
    <!-- <a-steps :current="currentStep" :items="steps" /> -->
    <Steps v-model:active="currentStep" :steps="steps" style=" width: 80%; margin: 0 auto 40px;" />

    <div class="drawer-content">
      <First v-show="currentStep === 0" ref="firstRef" :init-state="initState" :is-edit="isEdit" />
      <Second v-show="currentStep === 1" ref="secondRef" :init-state="initState" :is-edit="isEdit" />
      <!-- <Third v-show="currentStep === 2" /> -->
    </div>
    <template #footer>
      <div v-if="currentStep === 0" class="flex items-center justify-end gap-[24px] mx-[20.3%]">
        <a-button @click="close">
          取消
        </a-button>
        <a-button class="warning-button" @click="saveDraft">
          存为草稿
        </a-button>
        <a-button type="primary" class="ml-2" @click="next">
          下一步
        </a-button>
      </div>
      <div v-if="currentStep === 1" class="flex items-center justify-end gap-[24px] mx-[20.3%]">
        <a-button @click="close">
          取消
        </a-button>
        <a-button class="warning-button" @click="saveDraft">
          存为草稿
        </a-button>
        <a-button type="primary" class="ml-2" @click="last">
          上一步
        </a-button>
        <a-button type="primary" class="ml-2" @click="onSubmit">
          提交
        </a-button>
      </div>
    </template>
  </CustomDrawer>
</template>

<style lang="scss" scoped>
:deep(.step-icon) {
  width: 40px;
  height: 40px;
}

:deep(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: bold;
  color: #161e5d !important;
}

:deep(.ant-steps-item-description) {
  color: #60739b !important;
}

:deep(.ant-steps-item-content) {
  position: relative;
  padding-bottom: 20px;
}

:deep(.ant-steps-item-active) {
  .ant-steps-item-title {
    color: #2469f1 !important;
  }

  .ant-steps-item-description {
    color: #2469f1 !important;
  }

  .ant-steps-item-content {
    &::after {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 0;
      content: "";
      border: 8px solid;
      border-color: #2469f1 transparent transparent;
    }
  }
}

.drawer-content {
  flex: 1;
  margin-top: 20px;
  overflow-y: auto;
  border-top: 1px dashed #e7e7e7;
}
</style>
