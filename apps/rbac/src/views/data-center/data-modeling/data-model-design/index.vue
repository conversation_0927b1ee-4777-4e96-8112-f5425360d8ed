<script setup lang="tsx">
import type { ProColumns, ProTableProps } from '@pubinfo/pro-components';
import type { CurrentNode, DataModelItem, Query } from './types';
import { ProTable } from '@pubinfo/pro-components';
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import DataModelDrawer from './components/data-model-drawer/index.vue';
import DataCategoryTree from './components/dataCategoryTree.vue';
import ModelVersionDrawer from './components/model-version-drawer/index.vue';
import PublishModelDrawer from './components/publishModelDrawer.vue';

defineOptions({
  name: 'ModelDesignIndex',
});

const router = useRouter();

const columns: ProColumns<DataModelItem> = [
  {
    width: 80,
    title: '序号',
    dataIndex: 'index',
    fixed: 'left',
  },
  {
    width: 100,
    title: '模型名称',
    dataIndex: 'modelName',
  },
  {
    width: 100,
    title: '模型表名',
    dataIndex: 'modelTableName',
  },
  {
    width: 100,
    title: '建模模式',
    dataIndex: 'modelingMode',
  },
  {
    width: 100,
    title: '数仓分层',
    dataIndex: 'dwLayer',
  },
  {
    width: 100,
    title: '发布状态',
    dataIndex: 'publishStatus',
  },
  {
    width: 100,
    title: '模型状态',
    dataIndex: 'modelStatus',
  },
  {
    width: 100,
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    width: 280,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
];

const dataModelDrawer = ref();
const modelVersionDrawer = ref();
const publishModelDrawer = ref();
const currentNode = ref<CurrentNode>({});
const query = ref<Query>({
  orgId: '',
});
const searchParams = reactive({
  // dwLayer: undefined,
  text: '',
});
const tableRef = ref();

type tempKey = string | number;
const rowSelection: ProTableProps['rowSelection'] = {
  onChange: (selectedRowKeys: tempKey[], selectedRows: DataModelItem[]) => {
    // console.log(`selectedRowKeys: ${selectedRowKeys}`, typeof selectedRowKeys);
  },
  // getCheckboxProps: (record: DataType) => ({
  //   disabled: record.name === 'Disabled User', // Column configuration not to be checked
  //   name: record.name,
  // }),
};

// 添加轮询相关变量
const pollingTimer = ref<number | null>(null);
const pollingInterval = 10000; // 轮询间隔，10秒

// 开始轮询函数
function startPolling() {
  // 清除可能存在的轮询
  stopPolling();

  // 如果没有选中节点，不进行轮询
  if (!currentNode.value.nodeId) {
    return;
  }

  // 设置定时器进行轮询
  pollingTimer.value = window.setInterval(() => {
    // 静默刷新，不触发加载状态
    tableRef.value?.fetch({ silent: true });
  }, pollingInterval);
}

// 停止轮询函数
function stopPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
}

async function request(params: any) {
  const { current, pageSize, ...rest } = params;

  if (!currentNode.value.nodeId) {
    return {
      success: true,
      data: [],
      total: 0,
    };
  }

  const res = await postDataWarehouseModelPage({
    currentPage: current,
    pageSize,
    typeCode: currentNode.value.nodeType === 'first' ? currentNode.value.nodeId : undefined,
    layerid: currentNode.value.nodeType === 'second' ? Number(currentNode.value.nodeId) : undefined,
    content: searchParams.text,
  });

  if (res.success) {
    const tempData = res.data?.records?.map((item) => {
      return {
        id: item.id,
        modelName: item.modelName,
        modelTableName: item.tableName,
        modelingMode: item.createTypeInfo,
        dwLayer: item.layerName,
        publishStatus: item.publishStatus,
        createTime: item.createTime,
        modelStatus: item.statusInfo,
      };
    });
    return {
      success: true,
      data: tempData,
      total: res.data?.total,
    };
  }
  else {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
}

function onSearch() {
  tableRef.value.reload();
  // 重新开始轮询
  startPolling();
}

function onSelect(node: { nodeType: string, nodeId: string }) {
  currentNode.value = {
    nodeId: node.nodeId,
    nodeType: node.nodeType,
  };
  searchParams.text = '';
  tableRef.value.reload();
  // 重新开始轮询
  startPolling();
}

function createDataModel() {
  dataModelDrawer.value.open();
}

function editDataModel(record: DataModelItem) {
  dataModelDrawer.value.open(record);
}

function viewVersionModel(record: DataModelItem) {
  modelVersionDrawer.value.open(record);
}

function onSave() {
  tableRef.value.reload();
  // 重新开始轮询
  startPolling();
}

async function deleteDataModel(record: DataModelItem) {
  const res = await postDataWarehouseModelDelete({
    id: record.id!,
  });
  if (res.success) {
    message.success('删除成功');
    tableRef.value.reload();
    // 重新开始轮询
    startPolling();
  }
}

async function publishDataModel(record: DataModelItem) {
  publishModelDrawer.value.open(record);
}

function viewTaskInstance(record: DataModelItem) {
  router.push({
    name: 'ModelDesignTaskList',
    query: {
      modelId: record.id,
    },
  });
}

// 在组件挂载时开始轮询
onMounted(() => {
  // 开始轮询（如果有选中节点）
  if (currentNode.value.nodeId) {
    startPolling();
  }
});

// 在组件卸载前停止轮询
onUnmounted(() => {
  stopPolling();
});

// 监听表格查询条件变化，重新开始轮询
watch(() => tableRef.value?.query, () => {
  if (tableRef.value && currentNode.value.nodeId) {
    startPolling();
  }
}, { deep: true });

// 监听当前节点变化
watch(() => currentNode.value.nodeId, (newVal) => {
  if (newVal) {
    startPolling();
  }
  else {
    stopPolling();
  }
});
</script>

<template>
  <div
    w-full
    h-full
    py-29px
    px-40px
    flex
    flex-col
  >
    <div class="py-24px px-30px bg-[rgba(255,255,255,0.6)] rounded-t-[6px]">
      <div class="text-[20px] text-[#161E5D] font-[600] leading-[23px]">
        模型设计
      </div>
    </div>
    <div class="flex-auto bg-[#FFF] p-[30px]">
      <a-row :gutter="[8, 8]" :wrap="false" class="h-full">
        <a-col flex="300px" class="h-full overflow-hidden">
          <DataCategoryTree @select="onSelect" />
        </a-col>
        <a-col flex="auto" class="h-full flex flex-col">
          <div class="flex flex-wrap items-center justify-between gap-[10px] mb-[24px] px-[16px]">
            <a-space>
              搜索内容：
              <!-- <a-select v-model:value="searchParams.dwLayer" placeholder="请选择内容" :options="[]" /> -->
              <a-input-search v-model:value="searchParams.text" placeholder="可搜索模型名称和模型表名" :maxlength="30" @search="onSearch" />
            </a-space>
            <a-space>
              <a-button type="primary" @click="createDataModel">
                <template #icon>
                  <PlusOutlined />
                </template>
                数据建模
              </a-button>
              <!-- <a-button danger>
                <template #icon>
                  <DeleteOutlined />
                </template>
                批量删除
              </a-button> -->
            </a-space>
          </div>
          <div class="flex-auto">
            <ProTable
              ref="tableRef"
              row-key="id"
              :request="request"
              :columns="columns"
              :search="false"
              :toolbar="false"
              :row-selection="rowSelection"
              :scroll="{ x: 800 }"
            >
              <template #bodyCell="{ index, column, record }">
                <template v-if="column.dataIndex === 'index'">
                  {{ index + 1 }}
                </template>
                <template v-if="column.dataIndex === 'publishStatus'">
                  <a-tag v-if="record.publishStatus === 1" color="blue">
                    已发布
                  </a-tag>
                  <a-tag v-else-if="record.publishStatus === 2" color="green">
                    可发布
                  </a-tag>
                  <a-tag v-else-if="record.publishStatus === 3" color="orange">
                    发布中
                  </a-tag>
                  <a-tag v-else color="red">
                    未发布
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'modelStatus'">
                  <a-tag v-if="record.modelStatus === 1" color="blue">
                    正式
                  </a-tag>
                  <a-tag v-else-if="record.modelStatus === 2" color="orange">
                    草稿
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <a-space>
                    <a-button type="link" class="flex items-center " @click="() => viewVersionModel(record)">
                      <EyeOutlined />
                      <span class="ml-4px">查看</span>
                    </a-button>
                    <a-button type="link" class="flex items-center " @click="() => editDataModel(record)">
                      <PubSvgIcon size="16px" name="edit-file-name" />
                      <span class="ml-4px">编辑</span>
                    </a-button>
                    <a-dropdown>
                      <a-button type="link" class="flex items-center ">
                        <MoreOutlined />
                        <span class="ml-4px">更多</span>
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item>
                            <a-button :disabled="record.publishStatus !== 2" type="link" class="flex items-center" @click="() => { publishDataModel(record); }">
                              <CloudUploadOutlined />
                              发布
                            </a-button>
                          </a-menu-item>
                          <a-menu-item>
                            <a-button type="link" class="flex items-center" @click="() => { viewTaskInstance(record); }">
                              <!-- <PubSvgIcon size="16px" name="task-instance" /> -->
                              <span class="ml-4px">任务实例</span>
                            </a-button>
                          </a-menu-item>
                          <a-menu-item>
                            <a-popconfirm
                              title="确定删除吗？"
                              ok-text="确定"
                              cancel-text="取消"
                              @confirm="() => { deleteDataModel(record); }"
                            >
                              <a-button type="link" class="flex items-center">
                                <PubSvgIcon name="delete" size="16px" class="text-[#F05F5F]" />
                                <span class="ml-4px text-[#F05F5F]">删除</span>
                              </a-button>
                            </a-popconfirm>
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </a-space>
                </template>
              </template>
            </ProTable>
          </div>
        </a-col>
      </a-row>
    </div>
    <DataModelDrawer ref="dataModelDrawer" @save="onSave" />
    <ModelVersionDrawer ref="modelVersionDrawer" />
    <PublishModelDrawer ref="publishModelDrawer" @submit="onSave" />
  </div>
</template>
