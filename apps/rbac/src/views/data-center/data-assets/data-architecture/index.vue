<script setup lang="tsx">
import type { ProTableColumns } from '@pubinfo/pro-components';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { ProTable } from '@pubinfo/pro-components';
import { message, Modal } from 'ant-design-vue';
import AddDrawer from './components/add-drawer.vue';
import HandlePermissionDrawer from './components/handlePermissionDrawer.vue';
import iconList from './constantIcon';

defineOptions({
  name: 'DataSourceManage',
});

const router = useRouter();

const tableRef = ref();
const addDrawer = ref();
const handlePermissionDrawer = ref();
async function request(params: any) {
  const { current, pageSize, ...rest } = params;
  const res = await postDataArchitectureDataProjectPageQuery({
    currentPage: current,
    pageSize,
    ...rest,
  });
  return {
    success: res.success,
    data: res.data?.records ?? [],
    total: res.data?.total ?? 0,
  };
}

const columns: ProTableColumns = [
  {
    valueType: 'a-input',
    bind: 'value',
    minWidth: 200,
    title: '板块名称',
    dataIndex: 'zhName',
  },
  {
    valueType: 'a-select',
    minWidth: 200,
    title: '描述',
    dataIndex: 'remark',
  },
  // {
  //   hideInSearch: true,
  //   minWidth: 200,
  //   title: '业务负责人',
  //   dataIndex: 'businessOwnerName',

  // },
  {
    hideInSearch: true,
    minWidth: 200,
    title: '板块架构',
    dataIndex: 'framework',
  },
  // {
  //   hideInSearch: true,
  //   minWidth: 150,
  //   title: '状态',
  //   dataIndex: 'status',
  //   customRender: ({ text }: { text: number }) => {
  //     switch (text) {
  //       case 0:
  //         return (<Badge status="default" text="未连接" />);
  //       case 1:
  //         return (<Badge status="success" text="已连接" />);
  //       case 2:
  //         return (<Badge status="error" text="连接失败" />);
  //       default:
  //         return (<Badge status="default" text="未连接" />);
  //     }
  //   },
  // },
  {
    hideInSearch: true,
    width: 300,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    // hideInTable() {
    //   return !auth([
    //     'role_edit',
    //     'role_remove',
    //   ]);
    // },
  },
];

function onAction(key, record) {
  addDrawer.value.open(key, record);
}
// 刷新数据
function updateData() {
  tableRef.value.fetch();
}

function deleteBtn(id: string | number) {
  Modal.confirm({
    title: '删除确认',
    icon: () => (<ExclamationCircleFilled style="color: #F05F5F" />),
    content: '是否确认删除该数据板块?',
    onOk() {
      // console.log('OK');
      postDataArchitectureDataProjectDeleted({ id }).then((res) => {
        if (res.success) {
          message.success('删除成功');
          updateData();
        }
      });
    },
    onCancel() {
      // console.log('Cancel');
    },
  });
}

function gotoViewPage(record) {
  router.push({
    name: 'data-architecture-edit',
    query: {
      id: record.id,
      zhName: record.zhName,
    },
  });
}

function handlePermission(record) {
  handlePermissionDrawer.value.open(record);
}
</script>

<template>
  <PubCustomPage title="数据板块">
    <ProTable ref="tableRef" :request="request" :columns="columns" :search="false" bordered>
      <template #toolbar>
        <a-button v-auth="'data_architecture_save'" type="primary" @click="onAction('新建数据板块')">
          <PubSvgIcon name="ant-design:plus-outlined" />
          <span class="ml-4px">新建数据板块</span>
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'zhName'">
          <div class="flex items-center">
            <div v-for="item in iconList" :key="item.value">
              <img v-if="item.value === record.icon" class="w-60px h-60px mr-16px" :src="item.icon" alt="">
            </div>
            <div>{{ record.zhName }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'framework'">
          <div>{{ `主题域 ${record.domainCount} 业务对象 ${record.entityCount}` }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a class="flex items-center" @click="gotoViewPage(record)">
              <EyeOutlined />
              <span class="ml-4px">查看</span>
            </a>
            <a v-auth="'data_architecture_save'" class="flex items-center" @click="onAction('编辑数据板块', record)">
              <PubSvgIcon name="edit" />
              <span class="ml-4px">编辑</span>
            </a>
            <a class="flex items-center" @click="handlePermission(record)">
              <!-- <img class="w-16px h-16px" src="@/assets/images/data-center/api-data/auth.png" alt=""> -->
              <PubSvgIcon name="auth" />
              <span class="ml-4px">权限管理</span>
            </a>
            <a v-auth="'data_architecture_del'" class="flex items-center text-red-500" @click="deleteBtn(record.id)">
              <PubSvgIcon name="remove" />
              <span class="ml-4px">删除</span>
            </a>
          </a-space>
        </template>
      </template>
    </ProTable>

    <AddDrawer ref="addDrawer" @submit="updateData" />
    <HandlePermissionDrawer ref="handlePermissionDrawer" @submit="updateData" />
  </PubCustomPage>
</template>

<style lang="scss" scoped>
.selected-tips {
  margin-left: 20px;
  font-size: 14px;
  color: rgb(255 196 0);
}
</style>
