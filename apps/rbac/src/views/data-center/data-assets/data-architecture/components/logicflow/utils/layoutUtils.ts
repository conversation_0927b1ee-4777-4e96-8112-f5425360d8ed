/**
 * LogicFlow 布局工具函数
 */

export interface NodeData {
  id: string;
  x: number;
  y: number;
  fx?: number;
  fy?: number;
  [key: string]: any;
}

export interface EdgeData {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  [key: string]: any;
}

/**
 * 力导向布局算法
 * 优化节点位置，减少连线重叠
 */
export function optimizeLayout(
  nodes: NodeData[], 
  edges: EdgeData[], 
  options: {
    width?: number;
    height?: number;
    iterations?: number;
    coolingFactor?: number;
  } = {}
): NodeData[] {
  const {
    width = 800,
    height = 600,
    iterations = 100,
    coolingFactor = 0.1
  } = options;

  const k = Math.sqrt((width * height) / nodes.length); // 理想距离
  const nodesCopy = nodes.map(node => ({ ...node, fx: 0, fy: 0 }));

  for (let iter = 0; iter < iterations; iter++) {
    // 计算斥力 - 所有节点之间相互排斥
    nodesCopy.forEach(node1 => {
      node1.fx = 0;
      node1.fy = 0;
      
      nodesCopy.forEach(node2 => {
        if (node1.id !== node2.id) {
          const dx = node1.x - node2.x;
          const dy = node1.y - node2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance > 0) {
            const force = k * k / distance;
            node1.fx += (dx / distance) * force;
            node1.fy += (dy / distance) * force;
          }
        }
      });
    });
    
    // 计算引力 - 有连线的节点之间相互吸引
    edges.forEach(edge => {
      const source = nodesCopy.find(n => n.id === edge.sourceNodeId);
      const target = nodesCopy.find(n => n.id === edge.targetNodeId);
      
      if (source && target) {
        const dx = target.x - source.x;
        const dy = target.y - source.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
          const force = distance * distance / k;
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;
          
          source.fx += fx;
          source.fy += fy;
          target.fx -= fx;
          target.fy -= fy;
        }
      }
    });
    
    // 应用力并冷却
    const temp = coolingFactor * (iterations - iter) / iterations;
    nodesCopy.forEach(node => {
      const force = Math.sqrt(node.fx * node.fx + node.fy * node.fy);
      if (force > 0) {
        const displacement = Math.min(force, temp);
        node.x += (node.fx / force) * displacement;
        node.y += (node.fy / force) * displacement;
        
        // 边界约束
        node.x = Math.max(100, Math.min(width - 100, node.x));
        node.y = Math.max(80, Math.min(height - 80, node.y));
      }
    });
  }
  
  return nodesCopy.map(({ fx, fy, ...node }) => node);
}

/**
 * 网格布局算法
 * 按网格排列节点，适合层次结构
 */
export function gridLayout(
  nodes: NodeData[],
  options: {
    nodeSpacing?: number;
    rowHeight?: number;
    nodesPerRow?: number;
    startX?: number;
    startY?: number;
  } = {}
): NodeData[] {
  const {
    nodeSpacing = 250,
    rowHeight = 150,
    nodesPerRow = 3,
    startX = 150,
    startY = 100
  } = options;

  return nodes.map((node, index) => {
    const row = Math.floor(index / nodesPerRow);
    const col = index % nodesPerRow;
    
    return {
      ...node,
      x: col * nodeSpacing + startX,
      y: row * rowHeight + startY
    };
  });
}

/**
 * 层次布局算法
 * 根据节点的层级关系进行布局
 */
export function hierarchicalLayout(
  nodes: NodeData[],
  edges: EdgeData[],
  options: {
    levelSpacing?: number;
    nodeSpacing?: number;
    startX?: number;
    startY?: number;
  } = {}
): NodeData[] {
  const {
    levelSpacing = 200,
    nodeSpacing = 150,
    startX = 100,
    startY = 100
  } = options;

  // 构建邻接表
  const adjacencyList = new Map<string, string[]>();
  const inDegree = new Map<string, number>();
  
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
    inDegree.set(node.id, 0);
  });
  
  edges.forEach(edge => {
    const sourceList = adjacencyList.get(edge.sourceNodeId) || [];
    sourceList.push(edge.targetNodeId);
    adjacencyList.set(edge.sourceNodeId, sourceList);
    
    const targetInDegree = inDegree.get(edge.targetNodeId) || 0;
    inDegree.set(edge.targetNodeId, targetInDegree + 1);
  });
  
  // 拓扑排序确定层级
  const levels: string[][] = [];
  const queue: string[] = [];
  const visited = new Set<string>();
  
  // 找到所有入度为0的节点作为第一层
  inDegree.forEach((degree, nodeId) => {
    if (degree === 0) {
      queue.push(nodeId);
    }
  });
  
  while (queue.length > 0) {
    const currentLevel: string[] = [];
    const levelSize = queue.length;
    
    for (let i = 0; i < levelSize; i++) {
      const nodeId = queue.shift()!;
      currentLevel.push(nodeId);
      visited.add(nodeId);
      
      // 处理邻接节点
      const neighbors = adjacencyList.get(nodeId) || [];
      neighbors.forEach(neighborId => {
        if (!visited.has(neighborId)) {
          const newInDegree = (inDegree.get(neighborId) || 0) - 1;
          inDegree.set(neighborId, newInDegree);
          
          if (newInDegree === 0) {
            queue.push(neighborId);
          }
        }
      });
    }
    
    if (currentLevel.length > 0) {
      levels.push(currentLevel);
    }
  }
  
  // 处理剩余的节点（可能存在环）
  const remainingNodes = nodes.filter(node => !visited.has(node.id));
  if (remainingNodes.length > 0) {
    levels.push(remainingNodes.map(node => node.id));
  }
  
  // 根据层级设置节点位置
  const positionedNodes = nodes.map(node => ({ ...node }));
  
  levels.forEach((level, levelIndex) => {
    const y = startY + levelIndex * levelSpacing;
    const totalWidth = (level.length - 1) * nodeSpacing;
    const startXForLevel = startX - totalWidth / 2;
    
    level.forEach((nodeId, nodeIndex) => {
      const node = positionedNodes.find(n => n.id === nodeId);
      if (node) {
        node.x = startXForLevel + nodeIndex * nodeSpacing;
        node.y = y;
      }
    });
  });
  
  return positionedNodes;
}

/**
 * 检测并解决节点重叠
 */
export function resolveOverlaps(
  nodes: NodeData[],
  nodeWidth: number = 180,
  nodeHeight: number = 60,
  minSpacing: number = 20
): NodeData[] {
  const resolvedNodes = nodes.map(node => ({ ...node }));
  const padding = minSpacing;
  
  for (let i = 0; i < resolvedNodes.length; i++) {
    for (let j = i + 1; j < resolvedNodes.length; j++) {
      const node1 = resolvedNodes[i];
      const node2 = resolvedNodes[j];
      
      const dx = Math.abs(node1.x - node2.x);
      const dy = Math.abs(node1.y - node2.y);
      
      const minDx = nodeWidth + padding;
      const minDy = nodeHeight + padding;
      
      if (dx < minDx && dy < minDy) {
        // 发生重叠，调整位置
        const overlapX = minDx - dx;
        const overlapY = minDy - dy;
        
        if (overlapX < overlapY) {
          // 水平方向调整
          if (node1.x < node2.x) {
            node2.x += overlapX / 2;
            node1.x -= overlapX / 2;
          } else {
            node1.x += overlapX / 2;
            node2.x -= overlapX / 2;
          }
        } else {
          // 垂直方向调整
          if (node1.y < node2.y) {
            node2.y += overlapY / 2;
            node1.y -= overlapY / 2;
          } else {
            node1.y += overlapY / 2;
            node2.y -= overlapY / 2;
          }
        }
      }
    }
  }
  
  return resolvedNodes;
}
