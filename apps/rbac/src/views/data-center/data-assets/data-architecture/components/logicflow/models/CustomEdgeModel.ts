import type { BaseNodeModel } from '@logicflow/core';
import { PolylineEdgeModel } from '@logicflow/core';

/**
 * 自定义边模型 - 智能锚点选择，减少连线重叠
 */
export class CustomEdgeModel extends PolylineEdgeModel {
  /**
   * 智能选择起始锚点
   * 根据节点相对位置选择最佳锚点，减少连线重叠
   */
  getBeginAnchor(sourceNode: BaseNodeModel, targetNode: BaseNodeModel) {
    const sourceAnchors = sourceNode.anchors;
    const targetAnchors = targetNode.anchors;

    if (!sourceAnchors || !targetAnchors) {
      return super.getBeginAnchor(sourceNode, targetNode);
    }

    // 计算节点间的相对位置
    const dx = targetNode.x - sourceNode.x;
    const dy = targetNode.y - sourceNode.y;

    let sourceAnchor;

    // 根据相对位置选择最佳的输出锚点
    if (Math.abs(dx) > Math.abs(dy)) {
      // 水平方向距离更大
      if (dx > 0) {
        // 目标在右侧，选择右侧输出锚点
        sourceAnchor = sourceAnchors.find(a => a.name === 'right' && a.type === 'output');
      }
      else {
        // 目标在左侧，选择左侧锚点（如果有的话）
        sourceAnchor = sourceAnchors.find(a => a.name === 'left' && a.type === 'output');
      }
    }
    else {
      // 垂直方向距离更大
      if (dy > 0) {
        // 目标在下方，选择下方输出锚点
        sourceAnchor = sourceAnchors.find(a => a.name === 'bottom' && a.type === 'output');
      }
      else {
        // 目标在上方，选择上方锚点（如果有的话）
        sourceAnchor = sourceAnchors.find(a => a.name === 'top' && a.type === 'output');
      }
    }

    // 如果没找到合适的锚点，使用默认的输出锚点
    if (!sourceAnchor) {
      sourceAnchor = sourceAnchors.find(a => a.type === 'output') || sourceAnchors[0];
    }

    return sourceAnchor;
  }

  /**
   * 智能选择结束锚点
   * 根据源节点位置选择最佳的输入锚点
   */
  getEndAnchor(targetNode: BaseNodeModel) {
    const sourceNode = this.graphModel.getNodeModelById(this.sourceNodeId);
    const targetAnchors = targetNode.anchors;

    if (!sourceNode || !targetAnchors) {
      return super.getEndAnchor(targetNode);
    }

    // 计算节点间的相对位置
    const dx = targetNode.x - sourceNode.x;
    const dy = targetNode.y - sourceNode.y;

    let targetAnchor;

    // 根据相对位置选择最佳的输入锚点
    if (Math.abs(dx) > Math.abs(dy)) {
      // 水平方向距离更大
      if (dx > 0) {
        // 源在左侧，选择左侧输入锚点
        targetAnchor = targetAnchors.find(a => a.name === 'left' && a.type === 'input');
      }
      else {
        // 源在右侧，选择右侧锚点（如果有的话）
        targetAnchor = targetAnchors.find(a => a.name === 'right' && a.type === 'input');
      }
    }
    else {
      // 垂直方向距离更大
      if (dy > 0) {
        // 源在上方，选择上方输入锚点
        targetAnchor = targetAnchors.find(a => a.name === 'top' && a.type === 'input');
      }
      else {
        // 源在下方，选择下方锚点（如果有的话）
        targetAnchor = targetAnchors.find(a => a.name === 'bottom' && a.type === 'input');
      }
    }

    // 如果没找到合适的锚点，使用默认的输入锚点
    if (!targetAnchor) {
      targetAnchor = targetAnchors.find(a => a.type === 'input') || targetAnchors[0];
    }

    return targetAnchor;
  }

  /**
   * 保存边数据时包含锚点信息
   * 用于精确记录连接关系
   */
  getData() {
    const data = super.getData();
    data.sourceAnchorId = this.sourceAnchorId;
    data.targetAnchorId = this.targetAnchorId;
    return data;
  }

  /**
   * 自定义边的样式
   */
  getEdgeStyle() {
    const style = super.getEdgeStyle();

    style.stroke = '#1890ff';
    style.strokeWidth = 2;
    style.fill = 'none';

    // 添加动画效果（可选）
    if (this.isSelected) {
      style.strokeDasharray = '5,5';
      style.strokeDashoffset = '10';
    }

    return style;
  }

  /**
   * 自定义箭头配置
   */
  getArrowStyle() {
    const style = super.getArrowStyle();

    style.fill = '#1890ff';
    style.stroke = '#1890ff';

    return style;
  }

  /**
   * 自定义文本位置
   * 将文本放在连线的中点偏上位置
   */
  getTextPosition() {
    const position = super.getTextPosition();

    // 将文本稍微向上偏移，避免与连线重叠
    position.y -= 10;

    return position;
  }
}
