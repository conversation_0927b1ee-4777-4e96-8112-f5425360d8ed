import type { ConnectRule } from '@logicflow/core';
import { RectNodeModel } from '@logicflow/core';

/**
 * 自定义节点模型 - 支持分离的入口和出口锚点
 */
export class CustomNodeModel extends RectNodeModel {
  /**
   * 定义节点的默认锚点
   * 分离入口和出口，避免连线混乱
   */
  getDefaultAnchor() {
    const { width, height, x, y, id } = this;
    return [
      // 左侧锚点 - 作为入口
      {
        x: x - width / 2,
        y,
        name: 'left',
        id: `${id}_input_left`,
        edgeAddable: true,
        type: 'input', // 标记为输入锚点
      },
      // 右侧锚点 - 作为出口
      {
        x: x + width / 2,
        y,
        name: 'right',
        id: `${id}_output_right`,
        edgeAddable: true,
        type: 'output', // 标记为输出锚点
      },
      // 上方锚点 - 作为入口
      {
        x,
        y: y - height / 2,
        name: 'top',
        id: `${id}_input_top`,
        edgeAddable: true,
        type: 'input',
      },
      // 下方锚点 - 作为出口
      {
        x,
        y: y + height / 2,
        name: 'bottom',
        id: `${id}_output_bottom`,
        edgeAddable: true,
        type: 'output',
      },
    ];
  }

  /**
   * 设置节点的基本属性
   */
  setAttributes() {
    this.width = 180;
    this.height = 60;
    this.radius = 8;
  }

  /**
   * 连接规则 - 作为源节点时的规则
   * 确保只能从输出锚点连出
   */
  getConnectedSourceRules(): ConnectRule[] {
    const rules = super.getConnectedSourceRules();

    const outputOnlyRule: ConnectRule = {
      message: '只能从输出锚点（右侧/下方）连出',
      validate: (source, target, sourceAnchor, targetAnchor) => {
        // 检查源锚点是否为输出类型
        return sourceAnchor?.type === 'output';
      },
    };

    rules.push(outputOnlyRule);
    return rules;
  }

  /**
   * 连接规则 - 作为目标节点时的规则
   * 确保只能连接到输入锚点
   */
  getConnectedTargetRules(): ConnectRule[] {
    const rules = super.getConnectedTargetRules();

    const inputOnlyRule: ConnectRule = {
      message: '只能连接到输入锚点（左侧/上方）',
      validate: (source, target, sourceAnchor, targetAnchor) => {
        // 检查目标锚点是否为输入类型
        return targetAnchor?.type === 'input';
      },
    };

    rules.push(inputOnlyRule);
    return rules;
  }

  /**
   * 自定义锚点样式
   */
  getAnchorStyle(anchorInfo: any) {
    const style = super.getAnchorStyle(anchorInfo);

    // 根据锚点类型设置不同颜色
    if (anchorInfo.type === 'input') {
      style.stroke = '#52c41a'; // 绿色表示输入
      style.fill = '#f6ffed';
    }
    else if (anchorInfo.type === 'output') {
      style.stroke = '#1890ff'; // 蓝色表示输出
      style.fill = '#f0f9ff';
    }

    style.r = 4;
    style.hover = {
      ...style.hover,
      r: 6,
      fill: anchorInfo.type === 'input' ? '#52c41a' : '#1890ff',
      stroke: anchorInfo.type === 'input' ? '#52c41a' : '#1890ff',
    };

    return style;
  }

  /**
   * 自定义锚点连线样式
   */
  getAnchorLineStyle(anchorInfo: any) {
    const style = super.getAnchorLineStyle(anchorInfo);

    // 根据锚点类型设置连线颜色
    if (anchorInfo.type === 'input') {
      style.stroke = '#52c41a';
    }
    else if (anchorInfo.type === 'output') {
      style.stroke = '#1890ff';
    }

    style.strokeWidth = 2;
    style.strokeDasharray = '5,5';

    return style;
  }
}
