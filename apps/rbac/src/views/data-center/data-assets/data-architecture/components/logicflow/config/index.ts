import type { Definition } from '@logicflow/core';

/**
 * LogicFlow 配置选项
 */
export const logicFlowConfig = {
  // 基础配置
  width: 800,
  height: 600,
  isSilentMode: true, // 只读模式

  // 边配置
  edgeType: 'polyline', // 默认使用折线，减少重叠

  // 网格配置
  grid: {
    size: 20,
    visible: true,
    type: 'dot' as const,
  },

  // 键盘快捷键
  keyboard: {
    enabled: false, // 只读模式下禁用键盘操作
  },

  // 其他配置
  adjustEdge: false, // 禁用边的调整
  adjustEdgeStartAndEnd: false, // 禁用边起点终点调整
  adjustNodePosition: false, // 禁用节点位置调整

  // 自定义边生成器
  edgeGenerator: (sourceNode: any, targetNode: any, currentEdge?: any) => {
    return 'custom-edge';
  },
};

/**
 * LogicFlow 主题配置
 */
export const logicFlowTheme = {
  // 基础节点样式
  baseNode: {
    fill: '#ffffff',
    stroke: '#1890ff',
    strokeWidth: 2,
  },

  // 矩形节点样式
  rect: {
    fill: '#ffffff',
    stroke: '#1890ff',
    strokeWidth: 2,
    radius: 8,
  },

  // 锚点样式
  anchor: {
    stroke: '#1890ff',
    fill: '#ffffff',
    r: 4,
    hover: {
      r: 6,
      fill: '#1890ff',
      stroke: '#1890ff',
      fillOpacity: 0.8,
    },
  },

  // 锚点连线样式
  anchorLine: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: '5,5',
  },

  // 边样式
  polyline: {
    stroke: '#1890ff',
    strokeWidth: 2,
    fill: 'none',
    hoverStroke: '#40a9ff',
    selectedStroke: '#096dd9',
  },

  // 箭头样式
  arrow: {
    offset: 10,
    verticalLength: 5,
    fill: '#1890ff',
    stroke: '#1890ff',
  },

  // 节点文本样式
  nodeText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: 'normal',
    textAnchor: 'middle',
    dominantBaseline: 'middle',
  },

  // 边文本样式
  edgeText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: 'normal',
    textAnchor: 'middle',
    dominantBaseline: 'middle',
    background: {
      fill: '#ffffff',
      stroke: '#d9d9d9',
      strokeWidth: 1,
      rx: 4,
      ry: 4,
      padding: [4, 8],
    },
  },

  // 选中状态样式
  outline: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: '5,5',
    fill: 'none',
  },

  // 网格线样式
  snapline: {
    stroke: '#d9d9d9',
    strokeWidth: 1,
  },
};

/**
 * 节点注册配置
 */
export const nodeRegistrations: Definition[] = [
  {
    type: 'custom-rect',
    model: 'CustomNodeModel', // 将在主文件中动态设置
    view: 'RectNode', // 将在主文件中动态设置
  },
];

/**
 * 边注册配置
 */
export const edgeRegistrations: Definition[] = [
  {
    type: 'custom-edge',
    model: 'CustomEdgeModel', // 将在主文件中动态设置
    view: 'PolylineEdge', // 将在主文件中动态设置
  },
];

/**
 * 数据转换配置
 */
export const dataConversionConfig = {
  // 节点布局配置
  layout: {
    nodeSpacing: 250, // 节点间距
    rowHeight: 150, // 行高
    nodesPerRow: 3, // 每行节点数
    startX: 150, // 起始X坐标
    startY: 100, // 起始Y坐标
  },

  // 节点尺寸配置
  nodeSize: {
    width: 180,
    height: 60,
  },

  // 布局算法选项
  layoutAlgorithm: 'hierarchical' as 'grid' | 'hierarchical' | 'force', // 默认使用网格布局

  // 力导向布局配置
  forceLayoutOptions: {
    iterations: 100,
    coolingFactor: 0.1,
  },

  // 层次布局配置
  hierarchicalLayoutOptions: {
    levelSpacing: 200,
    nodeSpacing: 150,
  },
};

/**
 * 获取完整的LogicFlow配置
 */
export function getLogicFlowConfig(container: HTMLElement) {
  return {
    container,
    ...logicFlowConfig,
    style: logicFlowTheme,
  };
}
