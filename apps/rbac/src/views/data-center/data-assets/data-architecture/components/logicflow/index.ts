import LogicFlow, { PolylineEdge, RectNode } from '@logicflow/core';
import {
  dataConversionConfig,
  getLogicFlowConfig,
} from './config';
import { CustomEdgeModel } from './models/CustomEdgeModel';
import { CustomNodeModel } from './models/CustomNodeModel';
import {
  type EdgeData,
  gridLayout,
  hierarchicalLayout,
  type NodeData,
  optimizeLayout,
  resolveOverlaps,
} from './utils/layoutUtils';

/**
 * LogicFlow 管理类
 * 封装LogicFlow的初始化、配置和数据处理逻辑
 */
export class LogicFlowManager {
  private lf: LogicFlow | null = null;

  /**
   * 初始化LogicFlow实例
   */
  init(container: HTMLElement): LogicFlow {
    // 获取配置
    const config = getLogicFlowConfig(container);

    // 创建LogicFlow实例
    this.lf = new LogicFlow(config);

    // 注册自定义节点和边
    this.registerCustomElements();

    return this.lf;
  }

  /**
   * 注册自定义元素
   */
  private registerCustomElements() {
    if (!this.lf) {
      return;
    }

    // 注册自定义节点
    this.lf.register({
      type: 'custom-rect',
      view: RectNode,
      model: CustomNodeModel,
    });

    // 注册自定义边
    this.lf.register({
      type: 'custom-edge',
      view: PolylineEdge,
      model: CustomEdgeModel,
    });
  }

  /**
   * 渲染数据到LogicFlow
   */
  render(data: { nodes: any[], edges: any[] }) {
    if (!this.lf) {
      return;
    }

    this.lf.render(data);

    // 自动适应画布大小
    this.lf.fitView();
  }

  /**
   * 获取LogicFlow实例
   */
  getInstance(): LogicFlow | null {
    return this.lf;
  }

  /**
   * 销毁LogicFlow实例
   */
  destroy() {
    if (this.lf) {
      this.lf.destroy();
      this.lf = null;
    }
  }
}

/**
 * 数据转换工具类
 * 处理API数据到LogicFlow格式的转换
 */
export class DataConverter {
  /**
   * 扁平化树形数据
   */
  static flattenTreeData(data: any[]): any[] {
    const result: any[] = [];

    function traverse(items: any[]) {
      items.forEach((item) => {
        // 添加当前项的关系信息
        if (item.fromTableid && item.toTableid) {
          result.push({
            fromTableid: item.fromTableid,
            fromTableName: item.fromTableName,
            toTableid: item.toTableid,
            toTableName: item.toTableName,
            attribute: item.attribute,
          });
        }

        // 递归处理子项
        if (item.children && Array.isArray(item.children)) {
          traverse(item.children);
        }
      });
    }

    traverse(data);
    return result;
  }

  /**
   * 转换为LogicFlow数据格式
   */
  static convertToLogicFlowData(flattenedData: any[]): { nodes: NodeData[], edges: EdgeData[] } {
    const nodes: NodeData[] = [];
    const edges: EdgeData[] = [];
    const nodeMap = new Map();

    let nodeIndex = 0;

    flattenedData.forEach((item) => {
      // 创建源节点
      if (!nodeMap.has(item.fromTableid)) {
        const sourceNode: NodeData = {
          id: `node_${item.fromTableid}`,
          type: 'custom-rect',
          x: 0, // 临时坐标，后续会通过布局算法调整
          y: 0,
          text: item.fromTableName,
          properties: {
            tableId: item.fromTableid,
            tableName: item.fromTableName,
          },
        };

        nodes.push(sourceNode);
        nodeMap.set(item.fromTableid, sourceNode);
        nodeIndex++;
      }

      // 创建目标节点
      if (!nodeMap.has(item.toTableid)) {
        const targetNode: NodeData = {
          id: `node_${item.toTableid}`,
          type: 'custom-rect',
          x: 0, // 临时坐标
          y: 0,
          text: item.toTableName,
          properties: {
            tableId: item.toTableid,
            tableName: item.toTableName,
          },
        };

        nodes.push(targetNode);
        nodeMap.set(item.toTableid, targetNode);
        nodeIndex++;
      }

      // 创建连线
      const edge: EdgeData = {
        id: `edge_${item.fromTableid}_${item.toTableid}`,
        type: 'custom-edge',
        sourceNodeId: `node_${item.fromTableid}`,
        targetNodeId: `node_${item.toTableid}`,
        text: item.attribute,
        properties: {
          attribute: item.attribute,
        },
      };

      edges.push(edge);
    });

    // 应用布局算法
    const layoutedNodes = this.applyLayout(nodes, edges);

    return { nodes: layoutedNodes, edges };
  }

  /**
   * 应用布局算法
   */
  private static applyLayout(nodes: NodeData[], edges: EdgeData[]): NodeData[] {
    const { layout, layoutAlgorithm, forceLayoutOptions, hierarchicalLayoutOptions } = dataConversionConfig;

    let layoutedNodes: NodeData[];

    switch (layoutAlgorithm) {
      case 'hierarchical':
        layoutedNodes = hierarchicalLayout(nodes, edges, hierarchicalLayoutOptions);
        break;
      case 'force': {
        // 先用网格布局给初始位置
        const gridNodes = gridLayout(nodes, layout);
        layoutedNodes = optimizeLayout(gridNodes, edges, forceLayoutOptions);
        break;
      }
      case 'grid':
      default:
        layoutedNodes = gridLayout(nodes, layout);
        break;
    }

    // 解决重叠问题
    return resolveOverlaps(layoutedNodes, dataConversionConfig.nodeSize.width, dataConversionConfig.nodeSize.height);
  }
}

/**
 * 创建LogicFlow管理器实例
 */
export function createLogicFlowManager(): LogicFlowManager {
  return new LogicFlowManager();
}

// 导出所有需要的类型和工具
export {
  CustomEdgeModel,
  CustomNodeModel,
  dataConversionConfig,
  getLogicFlowConfig,
  gridLayout,
  hierarchicalLayout,
  optimizeLayout,
  resolveOverlaps,
};

export type { EdgeData, NodeData };
