<script setup lang="tsx">
import { useToggle } from '@vueuse/core';
import { getDataArchitectureDataProjectDomainEntityGetRelation } from '@/api/modules/project/shujuzichanshujujiagou';
import CustomDrawer from '@/components/CustomDrawer/index.vue';
import { createLogicFlowManager, DataConverter } from './logicflow';
import '@logicflow/core/lib/style/index.css';

defineOptions({
  name: 'RelationshipDrawer',
});

const [open, setOpen] = useToggle(false);
const containerRef = ref<HTMLElement | null>(null);
const logicFlowManager = createLogicFlowManager();

/**
 * 初始化LogicFlow实例
 */
function initFlowInstance() {
  if (containerRef.value) {
    // 使用LogicFlow管理器初始化
    logicFlowManager.init(containerRef.value);
  }
}

/**
 * 打开抽屉并加载关联关系数据
 */
async function onOpen(record: any) {
  setOpen(true);

  try {
    const res = await getDataArchitectureDataProjectDomainEntityGetRelation({
      Tableid: record.id,
    });

    // 使用数据转换器处理API数据
    const flattenedData = res.data ? DataConverter.flattenTreeData(res.data) : [];

    // 将拍平后的数据转换为LogicFlow格式
    const logicFlowData = DataConverter.convertToLogicFlowData(flattenedData);
    console.warn('转换后的LogicFlow数据:', logicFlowData);

    // 初始化LogicFlow实例
    initFlowInstance();

    // 渲染数据到LogicFlow
    if (flattenedData.length > 0) {
      logicFlowManager.render(logicFlowData);
    }
    else {
      // 如果没有数据，渲染空图表
      logicFlowManager.render({ nodes: [], edges: [] });
    }
  }
  catch (error) {
    console.error('加载关联关系数据失败:', error);
    // 发生错误时也要初始化LogicFlow实例
    initFlowInstance();
    logicFlowManager.render({ nodes: [], edges: [] });
  }
}

/**
 * 关闭抽屉
 */
function close() {
  setOpen(false);
  // 销毁LogicFlow实例以释放资源
  logicFlowManager.destroy();
}

defineExpose({
  open: onOpen,
});

onMounted(() => {
  // 组件挂载时不需要做任何操作
  // LogicFlow实例会在打开抽屉时创建
});

onUnmounted(() => {
  // 组件卸载时确保销毁LogicFlow实例
  logicFlowManager.destroy();
});
</script>

<template>
  <CustomDrawer
    v-model:open="open"
    title="关联关系"
    placement="right"
    :width="800"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }" @close="close"
  >
    <div ref="containerRef" class="h-full w-full" />
  </CustomDrawer>
</template>
