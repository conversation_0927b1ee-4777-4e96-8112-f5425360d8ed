<script setup lang="ts">
import type { SelectProps, TreeSelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import type { FormExpose } from 'ant-design-vue/es/form/Form';
import type { firstStepForm } from '../types';
import { getUUID } from '@/utils/uuid';

const props = defineProps<{
  initData: firstStepForm
  id?: string
}>();

const dataSrcOptions = ref<TreeSelectProps['treeData']>([]);
const dataSectionOptions = ref<SelectProps['options']>([]);
const domainOptions = ref<TreeSelectProps['treeData']>([]);
const administratorOptions = ref<SelectProps['options']>([]);
const expandedKeys = ref<string[]>([]);

const formRef = ref<FormExpose>();
const form = ref(props.initData);

const rules: Record<string, Rule[]> = {
  taskName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  taskDesc: [{ required: true, message: '请输入资产描述', trigger: 'blur' }],
  dataSrc: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  dataSection: [{ required: true, message: '请选择数据板块', trigger: 'change' }],
  domain: [{ required: true, message: '请选择主题域', trigger: 'change' }],
  administrator: [{ required: true, message: '请选择管理员', trigger: 'change' }],
};

// 查找节点路径
function findParentPath(targetKey: string, treeData: any[]): string[] {
  const fieldNames = {
    children: 'children',
    key: 'id',
  };

  function dfs(data: any[], path: string[] = []): string[] | null {
    for (const item of data) {
      // 当前节点的key
      const key = item[fieldNames.key];

      // 如果找到目标节点
      if (key === targetKey) {
        return path;
      }

      // 如果有子节点，继续搜索
      if (item[fieldNames.children] && item[fieldNames.children].length > 0) {
        const foundPath = dfs(item[fieldNames.children], [...path, key]);
        if (foundPath) {
          return foundPath;
        }
      }
    }

    return null;
  }

  return dfs(treeData, []) || [];
}

// 处理树节点展开/折叠
function handleTreeExpand(keys: string[]) {
  // 如果是展开操作（keys比expandedKeys多）
  if (keys.length > expandedKeys.value.length) {
    // 找出新展开的节点
    const newExpandedKey = keys.find(key => !expandedKeys.value.includes(key));
    if (newExpandedKey) {
      // 获取该节点的所有父节点路径
      const parentKeys = findParentPath(newExpandedKey, domainOptions.value);
      // 只保留当前节点及其父节点的展开状态
      expandedKeys.value = [newExpandedKey, ...parentKeys];
    }
  }
  else {
    // 折叠操作，直接使用新的keys
    expandedKeys.value = keys;
  }
}

async function handleDataSectionChange(isEdit: boolean = false) {
  const res = await getDataArchitectureDataProjectDomainQueryTree(
    {
      projectid: form.value.dataSection!,
      id: '0',
    },
  );

  // console.log(res, 'res<<>>');

  domainOptions.value = res.data as TreeSelectProps['treeData'] || [];

  if (!isEdit) {
    form.value.domain = undefined;
    expandedKeys.value = [];
  }
  else {
    const parentKeys = findParentPath(form.value.domain!, domainOptions.value);
    expandedKeys.value = [form.value.domain!, ...parentKeys];
  }
}

async function getDataSourceList() {
  const res = await getAssetcatalogQueueSource();

  const tempData = res?.data?.map((unit) => {
    return {
      id: getUUID(),
      name: unit.zhName,
      disabled: true,
      children: unit?.data?.map((item: any) => {
        return {
          id: item.db_source_id,
          name: item.name,
        };
      }) ?? [],
    };
  }) ?? [];

  dataSrcOptions.value = tempData;
}

async function getDataSectionList() {
  const res = await postDataArchitectureDataProjectPageQuery({
    currentPage: 1,
    pageSize: 9999,
    doSearchTotal: true,
  });
  dataSectionOptions.value = res.data?.records?.map(item => ({
    label: item.zhName,
    value: item.id,
  })) || [];
}

async function getAdministratorList() {
  const params = {
    currentPage: 1,
    pageSize: 999,
    enable: true,
  };
  const res = await postBaseUserPageQuery(params);
  administratorOptions.value = res?.data?.records?.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  }) ?? [];
}

onMounted(() => {
  getDataSourceList();
  getDataSectionList();
  getAdministratorList();
  if (props.id) {
    const isEdit = true;
    handleDataSectionChange(isEdit);
  }
});

defineExpose({
  getFormData: () => {
    return formRef.value?.validateFields().then((values) => {
      return form.value;
    });
  },
});
</script>

<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="form"
    :rules="rules"
    :label-col="{ span: 12 }"
  >
    <div>
      <div class="font-['PingFang SC'] font-[500] text-[18px] leading-[21px] text-[#161E5D] ">
        <FileTextOutlined />
        基本信息
      </div>
      <a-divider />
      <div>
        <a-form-item label="名称" name="taskName">
          <a-input v-model:value="form.taskName" placeholder="请输入内容" :maxlength="30" />
        </a-form-item>
        <a-form-item label="资产描述" name="taskDesc">
          <a-textarea v-model:value="form.taskDesc" placeholder="请输入内容" />
        </a-form-item>
        <a-form-item label="选择数据源" name="dataSrc">
          <!-- <a-select
            v-model:value="form.dataSrc"
            placeholder="请选择"
            :options="dataSrcOptions"
          /> -->
          <a-tree-select
            v-model:value="form.dataSrc"
            show-search
            style="width: 100%;"
            :get-popup-container="triggerNode => triggerNode.parentNode"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择内容"
            allow-clear
            :tree-data="dataSrcOptions"
            tree-node-filter-prop="name"
            :field-names="{
              children: 'children',
              label: 'name',
              value: 'id',
            }"
          />
        </a-form-item>
      </div>
    </div>
    <div>
      <div class="font-['PingFang SC'] font-[500] text-[18px] leading-[21px] text-[#161E5D] ">
        <InboxOutlined />
        管理信息
      </div>
      <a-divider />
      <div>
        <a-form-item label="数据板块" name="dataSection">
          <a-select
            v-model:value="form.dataSection"
            placeholder="请选择"
            :options="dataSectionOptions"
            @change="() => handleDataSectionChange()"
          />
        </a-form-item>
        <a-form-item label="主题域" name="domain">
          <a-tree-select
            v-model:value="form.domain"
            show-search
            style="width: 100%;"
            :get-popup-container="triggerNode => triggerNode.parentNode"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择内容"
            allow-clear
            :tree-data="domainOptions"
            tree-node-filter-prop="name"
            :field-names="{
              children: 'children',
              label: 'name',
              value: 'id',
            }"
            :tree-expanded-keys="expandedKeys"
            @tree-expand="handleTreeExpand"
          />
        </a-form-item>
        <a-form-item label="管理员" name="administrator">
          <a-select
            v-model:value="form.administrator"
            placeholder="请选择"
            :options="administratorOptions"
          />
        </a-form-item>
      </div>
    </div>
  </a-form>
</template>
