<script setup lang="tsx">
import type { RedisClusterInfo } from '../types';
import { message, Modal } from 'ant-design-vue';
import EditFlinkCluster from './editFlinkCluster.vue';

const editFlinkClusterRef = ref<InstanceType<typeof EditFlinkCluster>>();

const dataList = ref<RedisClusterInfo[]>([]);

async function getDatabaseList() {
  const res = await getInfoFlinkFindAll();

  dataList.value = res.data?.map((item: any) => {
    return {
      id: item.id,
      url: item.url,
      jobsCancelled: item.jobsCancelled,
      jobsFailed: item.jobsFailed,
      jobsFinished: item.jobsFinished,
      jobsRunning: item.jobsRunning,
      slotsAvailable: item.slotsAvailable,
      slotsTotal: item.slotsTotal,
      taskmanagers: item.taskmanagers,
    };
  }) || [];
}

function handleEdit(record?: RedisClusterInfo) {
  editFlinkClusterRef.value?.open(record);
}

async function handleDelete(record?: RedisClusterInfo) {
  Modal.confirm({
    title: '提示',
    content: '确定删除该集群吗？',
    onOk: async () => {
      await postInfoRedisDel({ id: record?.id as unknown as number });
      message.success('删除成功');
      getDatabaseList();
    },
  });
}

onMounted(() => {
  getDatabaseList();
});
</script>

<template>
  <div class="flex-auto bg-[#FFF] p-[30px] min-h-[400px]">
    <a-button type="primary" class="mb-20px" @click="() => handleEdit()">
      <template #icon>
        <PlusOutlined />
      </template>
      新增集群
    </a-button>
    <div class="dataList grid grid-cols-3 gap-4">
      <div v-for="(item, index) in dataList" :key="index" class="dataItem">
        <div class="dataItem-header flex items-center justify-between">
          <div class="dataItem-header-title flex items-center">
            <img class="w-20px h-20px ml-30px mr-10px" src="@/assets/images/data-center/data-assets/icon_data_base.png" alt="">
            <span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{{ item.name }}</span>
          </div>
          <div class="flex items-center pr-16px">
            <img src="@/assets/images/data-center/data-assets/icon_setting.png" class="cursor-pointer" alt="" @click="() => handleEdit(item)">
            <a-divider type="vertical" />
            <img src="@/assets/images/data-center/data-assets/icon_delete.png" class="cursor-pointer" alt="" @click="() => handleDelete(item)">
          </div>
        </div>
        <div class="p-20px flex items-center justify-between gap-16px">
          <div class="dataItem-pics flex items-center justify-center mb-20px">
            <img class="w-120px h-140px" src="@/assets/images/data-center/data-assets/logo_redis.png" alt="">
          </div>
          <div class="dataItem-details flex-1" />
        </div>
      </div>
    </div>
    <EditFlinkCluster ref="editFlinkClusterRef" @submit="getDatabaseList" />
  </div>
</template>

<style lang="scss" scoped>
.dataList {
  .dataItem {
    // height: 444px;
    border: 1px solid #e7e7e7;
    border-radius: 6px;

    .dataItem-header {
      .dataItem-header-title {
        width: 60%;
        height: 46px;
        padding-right: 10px;
        overflow: hidden;
        font-size: 20px;
        font-weight: 500;
        line-height: 46px;
        color: #fff;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-image: url("@/assets/images/data-center/data-assets/bg_redis.png");
        background-size: 100% 100%;

        img {
          flex-shrink: 0;
        }
      }
    }

    .database-type-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      margin-left: 10px;
      font-size: 24px;
      font-weight: bold;
      color: #2469f1;
    }

    .connection-count-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      margin-left: 10px;
      font-size: 20px;
      font-weight: bold;
      color: #2469f1;
    }

    .dataItem-details {
      .dataItem-details-item {
        >:first-child {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 30px;
          padding: 0 5px;
          overflow: hidden;
          font-size: 12px;
          line-height: 30px;
          color: #60739b;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
          background: #eff2ff;
          border-radius: 3px 3px 0 0;
        }

        >:last-child {
          height: 54px;
          font-size: 20px;
          line-height: 54px;
          color: #2469f1;
          text-align: center;
          background: #f9faff;
          border-radius: 0 0 3px 3px;
        }
      }
    }
  }
}
</style>
