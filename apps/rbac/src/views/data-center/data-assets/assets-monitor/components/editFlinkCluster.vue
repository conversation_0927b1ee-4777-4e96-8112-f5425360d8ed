<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import type { FormExpose } from 'ant-design-vue/es/form/Form';
import type { RedisClusterInfo } from '../types';
import { useToggle } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

defineOptions({
  name: 'EditFlinkCluster',
});

const emit = defineEmits(['submit']);

interface Form {
  /** id */
  id?: string
  /** 集群名称 */
  name?: string
  /** IP */
  ip?: string
  /** 端口 */
  port?: number
  /** 密码 */
  password?: string
}

const [open, setOpen] = useToggle(false);
const formRef = ref<FormExpose>();
const form = ref<Partial<Form>>({});
const state = reactive<{
  title: string
  initData: Partial<Form>
}>({
  title: '',
  initData: {
    id: undefined,
    name: undefined,
    ip: undefined,
    port: undefined,
    password: undefined,
  },
});

const rules = computed<Record<string, Rule[]>>(() => {
  return {
    name: [{ required: true, message: '请输入集群名称', trigger: 'blur' }],
    ip: [{ required: true, message: '请输入IP', trigger: 'blur' }],
    port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
    // password: [{ required: !form.value.id, message: '请输入密码', trigger: 'blur' }],
  };
});

async function onOpen(record?: RedisClusterInfo) {
  state.title = record && record.id ? '编辑Redis集群' : '添加Redis集群';

  if (record && record.id) {
    const res = await postInfoRedisGet({ id: record.id as unknown as number });

    form.value = {
      id: record.id,
      name: record.name,
      ip: record.host,
      port: record.port,
      password: record.password,
    };
    setOpen(true);
  }
  else {
    form.value = cloneDeep(state.initData);
    setOpen(true);
  }
}

const [loading, setLoading] = useToggle(false);
function onSubmit() {
  formRef.value?.validate().then(async () => {
    const params = {
      ...form.value,
    };

    const res = await postInfoRedisUpsert({
      id: params.id,
      name: params.name ?? '',
      host: params.ip ?? '',
      port: params.port ?? 0,
      password: params.password ?? '',
    });

    message.success(params.id ? '修改成功' : '添加成功');
    setOpen(false);
    emit('submit');
  });
}

onMounted(() => {
  // getTypeCoxde();
});

defineExpose({
  open: onOpen,
});
</script>

<template>
  <a-drawer
    v-model:open="open"
    :title="state.title"
    placement="right"
    :width="520"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :model="form"
      :rules="rules"
      :label-col="{ span: 12 }"
    >
      <a-form-item label="集群名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入集群名称" />
      </a-form-item>
      <a-form-item label="IP" name="ip">
        <a-input v-model:value="form.ip" placeholder="请输入IP" />
      </a-form-item>
      <a-form-item label="端口" name="port">
        <a-input v-model:value="form.port" autocomplete="off" placeholder="请输入端口" />
      </a-form-item>
      <a-form-item label="密码" name="password">
        <a-input-password v-model:value="form.password" placeholder="请输入" autocomplete="new-password" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="setOpen(false)">
          取消
        </a-button>
        <a-button type="primary" :loading="loading" @click="onSubmit()">
          保存
        </a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
