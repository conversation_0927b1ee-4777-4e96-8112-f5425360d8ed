<script setup lang="tsx">
import DataBase from './components/dataBase.vue';
import Flink from './components/flink.vue';
import Kafka from './components/kafka.vue';
import Redis from './components/redis.vue';
import Server from './components/server.vue';

const router = useRouter();

const activeKey = ref(1);

onMounted(() => {
  // tabChange();
});

// async function tabChange() {
//   switch (activeKey.value) {
//     case 1:
//       await postInfoServerGetAll().then((res) => {
//         dataList.value = res.data.map((item: any) => {
//           const tempDiskLeft = `${(Number.parseFloat(item.diskTotal) - Number.parseFloat(item.diskUsed)).toFixed(2)}G`;
//           const tempRamLeft = `${(Number.parseFloat(item.ramMax) - Number.parseFloat(item.ramCurrent)).toFixed(2)}G`;
//           return {
//             ip: item.ip,
//             cpuUsage: Math.round(item.cpuPercent) || 0,
//             memoryUsage: Math.round(Number.parseFloat(item.ramCurrent) / Number.parseFloat(item.ramMax) * 100) || 0,
//             loadAverage: item.loadAverage,
//             cpus: item.cpus,
//             diskTotal: item.diskTotal,
//             diskUsed: item.diskUsed,
//             diskLeft: tempDiskLeft,
//             ramMax: item.ramMax,
//             ramUsed: item.ramCurrent,

//             ramLeft: tempRamLeft,
//           };
//         });
//       });
//       createServersGauge('cpuUsage');
//       createServersGauge('memoryUsage');
//       break;
//     // case 2:
//     //   postKafkaGetAll().then((res: any) => {
//     //     dataList.value = res.data
//     //   })
//     //   break;
//     // case 3:
//     //   postServerGetAll().then((res: any) => {
//     //     dataList.value = res.data
//     //   })
//     //   break;
//     // case 4:
//     //   postRedisGetAll().then((res: any) => {
//     //     dataList.value = res.data
//     //   })
//     //   break;
//     // case 5:
//     //   postEsGetAll().then((res: any) => {
//     //     dataList.value = res.data
//     //   })
//     //   break;
//     default:
//       break;
//   }
// }

const tabs = [{
  name: '服务器',
  key: 1,
  // icon: new URL(`@/assets/images/data-gather/gather-1.png`, import.meta.url).href,
},
{
  name: '数据库',
  key: 2,
},
{
  name: 'Kafka',
  key: 3,
},
{
  name: 'Redis',
  key: 4,
},
{
  name: 'Flink',
  key: 5,
},
// {
//   name: 'ES',
//   key: 5,
// }
];
</script>

<template>
  <div class="w-full h-full py-29px px-40px flex flex-col">
    <div class="h-[108px] py-24px px-30px bg-[rgba(255,255,255,0.6)] rounded-t-[6px]">
      <div class="text-[20px] text-[#161E5D] font-semibold leading-[23px] mb-[20px]">
        资源监控
      </div>
      <a-tabs v-model:active-key="activeKey" type="card">
        <a-tab-pane v-for="tab in tabs" :key="tab.key">
          <template #tab>
            <div class="flex items-center">
              <!-- <DesktopOutlined /> -->
              <PubSvgIcon name="icon-bag" color="#1677ff" size="20px" class="mr-[16px] text-[#1677ff]" />
              {{ tab.name }}
            </div>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <div>
      <keep-alive>
        <Server v-if="activeKey === 1" />
      </keep-alive>
      <keep-alive>
        <DataBase v-if="activeKey === 2" />
      </keep-alive>
      <keep-alive>
        <Redis v-if="activeKey === 4" />
      </keep-alive>
      <keep-alive>
        <Kafka v-if="activeKey === 3" />
      </keep-alive>
      <keep-alive>
        <Flink v-if="activeKey === 5" />
      </keep-alive>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dataList {
  .dataItem {
    // width: 493px;
    height: 444px;
    border: 1px solid #e7e7e7;
    border-radius: 6px;

    .dataItem-header {
      width: 224.51px;
      height: 46px;
      font-size: 20px;
      font-weight: 500;
      line-height: 46px;
      color: #fff;
      background-image: url("@/assets/images/data-center/data-assets/ip-background.png");
    }

    .dataItem-details {
      .dataItem-details-item {
        >:first-child {
          height: 30px;
          font-size: 12px;
          line-height: 30px;
          color: #60739b;
          text-align: center;
          background: #eff2ff;
          border-radius: 3px 3px 0 0;
        }

        >:last-child {
          height: 54px;
          font-size: 20px;
          line-height: 54px;
          color: #2469f1;
          text-align: center;
          background: #f9faff;
          border-radius: 0 0 3px 3px;
        }
      }
    }
  }
}
</style>
