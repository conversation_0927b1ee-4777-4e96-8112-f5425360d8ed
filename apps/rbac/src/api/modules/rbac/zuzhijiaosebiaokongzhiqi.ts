import { basic as request } from '@/api/request';

/**
 * @description 根据OrgID获取组织所有角色 根据OrgID获取组织所有角色
 * @url /rbac/orgRole/getAllByOrgId
 * @method GET
 * <AUTHOR>
 */
export function getRbacOrgRoleGetAllByOrgId<
  R = API.ResponseDataListPubRole,
  T = API.ResponseDataListPubRole,
>(
  params: API.getRbacOrgRoleGetAllByOrgIdParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/rbac/orgRole/getAllByOrgId', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据OrgID获取组织角色详情 根据OrgID获取组织角色详情
 * @url /rbac/orgRole/getByOrgId
 * @method GET
 * <AUTHOR>
 */
export function getRbacOrgRoleGetByOrgId<
  R = API.ResponseDataPubOrgRoleBo,
  T = API.ResponseDataPubOrgRoleBo,
>(params: API.getRbacOrgRoleGetByOrgIdParams, options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>('/rbac/orgRole/getByOrgId', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 角色组织分页查询
 * @url /rbac/orgRole/roleOrgPage
 * @method POST
 * <AUTHOR>
 */
export function postRbacOrgRoleRoleOrgPage<
  R = API.ResponseDataPageDataPubOrgBo,
  T = API.ResponseDataPageDataPubOrgBo,
>(body: API.PubRoleQueryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/rbac/orgRole/roleOrgPage', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 更新组织角色表 根据请求体更新组织角色表
 * @url /rbac/orgRole/saveOrgRole
 * @method POST
 * <AUTHOR>
 */
export function postRbacOrgRoleSaveOrgRole<
  R = API.ResponseDataPubOrgRoleAddDto,
  T = API.ResponseDataPubOrgRoleAddDto,
>(body: API.PubOrgRoleAddDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/rbac/orgRole/saveOrgRole', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
