import { basic as request } from '@/api/request';

/**
 * @description 批量删除敏感标签 敏感标签
 * @url /dataSecurity/sensitiveLabel/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除敏感标签 敏感标签
 * @url /dataSecurity/sensitiveLabel/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 敏感标签详情 敏感标签
 * @url /dataSecurity/sensitiveLabel/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelDetail<
  R = API.ResponseDataSensitiveLabelDto,
  T = API.ResponseDataSensitiveLabelDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 敏感标签下拉 敏感标签
 * @url /dataSecurity/sensitiveLabel/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelOptions<
  R = API.ResponseDataListOptionVoStringLong,
  T = API.ResponseDataListOptionVoStringLong,
>(body: API.SensitiveLabelQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/options', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 敏感标签分页列表 敏感标签
 * @url /dataSecurity/sensitiveLabel/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelPageList<
  R = API.ResponseDataPageSensitiveLabelListVo,
  T = API.ResponseDataPageSensitiveLabelListVo,
>(body: API.SensitiveLabelQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加敏感标签 敏感标签
 * @url /dataSecurity/sensitiveLabel/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.SensitiveLabelDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改敏感标签 敏感标签
 * @url /dataSecurity/sensitiveLabel/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySensitiveLabelUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.SensitiveLabelDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/sensitiveLabel/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
