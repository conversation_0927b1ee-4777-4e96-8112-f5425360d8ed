import { basic as request } from '@/api/request';

/**
 * @description 规则模板下拉 规则模板
 * @url /dataQuality/ruleTemplate/options
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityRuleTemplateOptions<
  R = API.ResponseDataListOptionVoStringString,
  T = API.ResponseDataListOptionVoStringString,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataQuality/ruleTemplate/options',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description 规则模板分页列表 规则模板
 * @url /dataQuality/ruleTemplate/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityRuleTemplatePageList<
  R = API.ResponseDataPageRuleTemplateListVo,
  T = API.ResponseDataPageRuleTemplateListVo,
>(body: API.DataSecurityKeyQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/ruleTemplate/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
