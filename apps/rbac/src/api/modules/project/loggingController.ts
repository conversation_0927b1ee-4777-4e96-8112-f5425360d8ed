import { basic as request } from '@/api/request';

/**
 * @description 此处后端没有提供注释
 * @url /logging/systemLog
 * @method POST
 * <AUTHOR>
 */
export function postLoggingSystemLog<R = Record<string, any>, T = Record<string, any>>(
  body: API.SystemLog,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/logging/systemLog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
