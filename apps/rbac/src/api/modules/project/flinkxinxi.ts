import { basic as request } from '@/api/request';

/**
 * @description 新增flink
 * @url /info/flink/addFlink
 * @method GET
 * <AUTHOR>
 */
export function getInfoFlinkAddFlink<R = API.ResponseData, T = API.ResponseData>(
  params: API.getInfoFlinkAddFlinkParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/info/flink/addFlink', {
    params: {
      ...params,
      flinkMsg: undefined,
      ...params.flinkMsg,
    },
    ...(options || {}),
  });
}

/**
 * @description 删除flink
 * @url /info/flink/deletedFlink
 * @method GET
 * <AUTHOR>
 */
export function getInfoFlinkDeletedFlink<R = API.ResponseData, T = API.ResponseData>(
  params: API.getInfoFlinkDeletedFlinkParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/info/flink/deletedFlink', {
    params: {
      ...params,
      flinkMsg: undefined,
      ...params.flinkMsg,
    },
    ...(options || {}),
  });
}

/**
 * @description 查询flink信息
 * @url /info/flink/findAll
 * @method GET
 * <AUTHOR>
 */
export function getInfoFlinkFindAll<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/info/flink/findAll', {
    ...(options || {}),
  });
}
