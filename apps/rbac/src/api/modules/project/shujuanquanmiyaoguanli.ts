import { basic as request } from '@/api/request';

/**
 * @description 批量删除密钥 密钥管理
 * @url /dataSecurity/key/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/key/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除密钥 密钥管理
 * @url /dataSecurity/key/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyDel<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataSecurity/key/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 密钥详情 密钥管理
 * @url /dataSecurity/key/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyDetail<
  R = API.ResponseDataDataSecurityKeyDto,
  T = API.ResponseDataDataSecurityKeyDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/key/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 加密方式下拉 密钥管理
 * @url /dataSecurity/key/encryptionMethod/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyEncryptionMethodOptions<
  R = API.ResponseDataListEncryptionMethodVo,
  T = API.ResponseDataListEncryptionMethodVo,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataSecurity/key/encryptionMethod/options',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description 生成密钥 密钥管理
 * @url /dataSecurity/key/genKey
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyGenKey<
  R = API.ResponseDataEncryptInfo,
  T = API.ResponseDataEncryptInfo,
>(params: API.postDataSecurityKeyGenKeyParams, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataSecurity/key/genKey',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 导出密钥 密钥管理
 * @url /dataSecurity/key/importKey
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyImportKey<R = API.ResponseData, T = API.ResponseData>(
  body: API.EncryptInfo,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataSecurity/key/importKey', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 密钥下拉 密钥管理
 * @url /dataSecurity/key/keyOptions
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyKeyOptions<
  R = API.ResponseDataListOptionVoStringLong,
  T = API.ResponseDataListOptionVoStringLong,
>(body: API.DataSecurityKeyQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/key/keyOptions', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 密钥分页列表 密钥管理
 * @url /dataSecurity/key/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyPageList<
  R = API.ResponseDataPageDataSecurityKeyListVo,
  T = API.ResponseDataPageDataSecurityKeyListVo,
>(body: API.DataSecurityKeyQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/key/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加密钥 密钥管理
 * @url /dataSecurity/key/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeySave<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.DataSecurityKeyDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataSecurity/key/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改密钥 密钥管理
 * @url /dataSecurity/key/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyUpd<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.DataSecurityKeyDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataSecurity/key/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改密钥状态 密钥管理
 * @url /dataSecurity/key/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityKeyUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(
  params: API.postDataSecurityKeyUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataSecurity/key/updStatus',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
