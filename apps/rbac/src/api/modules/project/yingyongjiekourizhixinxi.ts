import { basic as request } from '@/api/request';

/**
 * @description 按小时查询 原接口 /apiinfo/byhuor
 * @url /userapi/byhuor
 * @method POST
 * <AUTHOR>
 */
export function postUserapiByhuor<R = Record<string, any>, T = Record<string, any>>(
  body: API.AppApiLogDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/byhuor', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 调用效率接口 原接口 /logInfo/callEfficiency
 * @url /userapi/callEfficiency
 * @method POST
 * <AUTHOR>
 */
export function postUserapiCallEfficiency<R = Record<string, any>, T = Record<string, any>>(
  body: API.AppApiLogDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/callEfficiency', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 调用日志接口 原接口 /logInfo/calllog
 * @url /userapi/calllog
 * @method POST
 * <AUTHOR>
 */
export function postUserapiCalllog<R = Record<string, any>, T = Record<string, any>>(
  body: API.AppApiLogDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/calllog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 日报接口 原接口 /apiinfo/dailyReport
 * @url /userapi/dailyReport
 * @method POST
 * <AUTHOR>
 */
export function postUserapiDailyReport<R = Record<string, any>, T = Record<string, any>>(
  body: API.AppApiLogDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/dailyReport', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 用户调用api接口日志分页列表
 * @url /userapi/detail/list
 * @method POST
 * <AUTHOR>
 */
export function postUserapiDetailList<R = API.ApiLog, T = API.ApiLog>(
  body: API.ApiLogQueryDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/detail/list', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 错误日志接口 原接口 /logInfo/errorLog
 * @url /userapi/errorLog
 * @method POST
 * <AUTHOR>
 */
export function postUserapiErrorLog<R = Record<string, any>, T = Record<string, any>>(
  body: API.AppApiLogDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/userapi/errorLog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
