import { basic as request } from '@/api/request';

/**
 * @description 获取 server 信息
 * @url /info/server/get
 * @method POST
 * <AUTHOR>
 */
export function postInfoServerGet<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoServerGetParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/server/get',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取全部 server 信息
 * @url /info/server/getAll
 * @method POST
 * <AUTHOR>
 */
export function postInfoServerGetAll<R = Record<string, any>, T = Record<string, any>>(
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/server/getAll',
    {},
    {
      ...(options || {}),
    },
  );
}
