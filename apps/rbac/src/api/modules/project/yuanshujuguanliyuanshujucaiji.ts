import { basic as request } from '@/api/request';

/**
 * @description 删除采集任务 采集任务
 * @url /data/meta/task/del
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskDel<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/data/meta/task/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询采集任务详情 采集任务
 * @url /data/meta/task/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskDetail<
  R = API.ResponseDataDataMetaTaskVo,
  T = API.ResponseDataDataMetaTaskVo,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/data/meta/task/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询采集任务 采集任务
 * @url /data/meta/task/page
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskPage<
  R = API.ResponseDataPageDataDataMetaTaskPageVo,
  T = API.ResponseDataPageDataDataMetaTaskPageVo,
>(body: API.DataMetaTaskPage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/data/meta/task/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 保存采集任务 采集任务
 * @url /data/meta/task/save
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskSave<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataMetaTaskDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/data/meta/task/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 停止工作流 采集任务
 * @url /data/meta/task/shutdown
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskShutdown<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDataMetaTaskShutdownParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/data/meta/task/shutdown',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取已采集所有数据源(下拉框) 元数据注册使用
 * @url /data/meta/task/source
 * @method GET
 * <AUTHOR>
 */
export function getDataMetaTaskSource<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/data/meta/task/source', {
    ...(options || {}),
  });
}

/**
 * @description 根据元数据采集结果id获取数据源、数据表信息 元数据注册使用
 * @url /data/meta/task/sourceByCollectId
 * @method GET
 * <AUTHOR>
 */
export function getDataMetaTaskSourceByCollectId<
  R = API.ResponseDataCollectTableSourceVo,
  T = API.ResponseDataCollectTableSourceVo,
>(
  params: API.getDataMetaTaskSourceByCollectIdParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/data/meta/task/sourceByCollectId', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 启动工作流 采集任务
 * @url /data/meta/task/startup
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskStartup<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDataMetaTaskStartupParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/data/meta/task/startup',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取指定数据源下采集的所有数据表(下拉框) 元数据注册使用
 * @url /data/meta/task/table
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTaskTable<R = API.ResponseData, T = API.ResponseData>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/data/meta/task/table', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 获取指定已采集的数据表所有字段列表 元数据注册使用
 * @url /data/meta/task/table/columns
 * @method GET
 * <AUTHOR>
 */
export function getDataMetaTaskTableColumns<
  R = API.ResponseDataMetaColumn,
  T = API.ResponseDataMetaColumn,
>(
  params: API.getDataMetaTaskTableColumnsParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/data/meta/task/table/columns', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
