import { basic as request } from '@/api/request';

/**
 * @description 批量删除任务 敏感数据识别
 * @url /dataSecurity/scanTask/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除任务 敏感数据识别
 * @url /dataSecurity/scanTask/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务详情 敏感数据识别
 * @url /dataSecurity/scanTask/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskDetail<
  R = API.ResponseDataDataSecurityScanTaskDto,
  T = API.ResponseDataDataSecurityScanTaskDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加到动态脱敏规则 敏感数据识别
 * @url /dataSecurity/scanTask/detectionResult/addDynamicMaskRule
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskDetectionResultAddDynamicMaskRule<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/detectionResult/addDynamicMaskRule', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查看数据表前十数据 敏感数据识别
 * @url /dataSecurity/scanTask/detectionResult/dataLimit10
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskDetectionResultDataLimit10<
  R = API.ResponseDataTableDataVo,
  T = API.ResponseDataTableDataVo,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/detectionResult/dataLimit10', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 检测结果分页列表 敏感数据识别
 * @url /dataSecurity/scanTask/detectionResult/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskDetectionResultPageList<
  R = API.ResponseDataPageDetectionResultVo,
  T = API.ResponseDataPageDetectionResultVo,
>(body: API.DataSecurityScanTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/detectionResult/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 敏感数据识别任务分页列表 敏感数据识别
 * @url /dataSecurity/scanTask/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskPageList<
  R = API.ResponseDataPageScanTaskListVo,
  T = API.ResponseDataPageScanTaskListVo,
>(body: API.DataSecurityScanTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加任务 敏感数据识别
 * @url /dataSecurity/scanTask/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DataSecurityScanTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加任务到草稿 敏感数据识别
 * @url /dataSecurity/scanTask/saveDraft
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskSaveDraft<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DataSecurityScanTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/saveDraft', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据表下拉 敏感数据识别
 * @url /dataSecurity/scanTask/tablePublishOption
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskTablePublishOption<
  R = API.ResponseDataListTablePublishVo,
  T = API.ResponseDataListTablePublishVo,
>(body: API.DataSecurityScanTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/tablePublishOption', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务日志 敏感数据识别
 * @url /dataSecurity/scanTask/taskLog
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskTaskLog<
  R = API.ResponseDataScanTaskLogVo,
  T = API.ResponseDataScanTaskLogVo,
>(body: API.DataSecurityScanTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/taskLog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改任务 敏感数据识别
 * @url /dataSecurity/scanTask/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityScanTaskUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DataSecurityScanTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/scanTask/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
