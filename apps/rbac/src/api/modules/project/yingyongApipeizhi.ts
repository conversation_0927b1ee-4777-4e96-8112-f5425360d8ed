import { basic as request } from '@/api/request';

/**
 * @description 获取所有配置接口
 * @url /api/appApi/findList
 * @method GET
 * <AUTHOR>
 */
export function getApiAppApiFindList<R = Record<string, any>, T = Record<string, any>>(
  params: API.getApiAppApiFindListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/api/appApi/findList', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 获取已配置接口
 * @url /api/appApi/findListYpz
 * @method GET
 * <AUTHOR>
 */
export function getApiAppApiFindListYpz<R = Record<string, any>, T = Record<string, any>>(
  params: API.getApiAppApiFindListYpzParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/api/appApi/findListYpz', {
    params: {
      ...params,
      page: undefined,
      ...params.page,
    },
    ...(options || {}),
  });
}

/**
 * @description 数据分布列表
 * @url /api/appApi/findParam
 * @method GET
 * <AUTHOR>
 */
export function getApiAppApiFindParam<R = Record<string, any>, T = Record<string, any>>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/api/appApi/findParam', {
    ...(options || {}),
  });
}
