import { basic as request } from '@/api/request';

/**
 * @description 查询数据类型下拉框 消息队列
 * @url /assetcatalog/queue/dataType
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogQueueDataType<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/assetcatalog/queue/dataType', {
    ...(options || {}),
  });
}

/**
 * @description 删除消息队列 消息队列
 * @url /assetcatalog/queue/deleted
 * @method POST
 * <AUTHOR>
 */
export function postAssetcatalogQueueDeleted<R = API.ResponseData, T = API.ResponseData>(
  params: API.postAssetcatalogQueueDeletedParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/assetcatalog/queue/deleted',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 查询指定消息队列 消息队列
 * @url /assetcatalog/queue/detail
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogQueueDetail<R = API.ResponseData, T = API.ResponseData>(
  params: API.getAssetcatalogQueueDetailParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/assetcatalog/queue/detail', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询消息队列 消息队列
 * @url /assetcatalog/queue/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postAssetcatalogQueuePageQuery<
  R = API.ResponseDataPageDataDataQueueListVo,
  T = API.ResponseDataPageDataDataQueueListVo,
>(body: API.DataQueuePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/assetcatalog/queue/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询消息队列下拉框 消息队列
 * @url /assetcatalog/queue/queryList
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogQueueQueryList<
  R = API.ResponseDataListDataQueueSimpleListVo,
  T = API.ResponseDataListDataQueueSimpleListVo,
>(
  params: API.getAssetcatalogQueueQueryListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/assetcatalog/queue/queryList', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 新增编辑消息队列 消息队列
 * @url /assetcatalog/queue/save
 * @method POST
 * <AUTHOR>
 */
export function postAssetcatalogQueueSave<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataQueueDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/assetcatalog/queue/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 获取消息队列所有数据源(下拉框) 消息队列
 * @url /assetcatalog/queue/source
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogQueueSource<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/assetcatalog/queue/source', {
    ...(options || {}),
  });
}

/**
 * @description 分页查询表数据 分页查询表数据
 * @url /assetcatalog/table/data/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postAssetcatalogTableDataPageQuery<
  R = API.ResponseDataPageDataMapStringObject,
  T = API.ResponseDataPageDataMapStringObject,
>(body: API.TableDataQueryPage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/assetcatalog/table/data/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询指定数据表 数据表
 * @url /assetcatalog/table/detail
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogTableDetail<
  R = API.ResponseDataDataProjectDomainTableDetailVo,
  T = API.ResponseDataDataProjectDomainTableDetailVo,
>(params: API.getAssetcatalogTableDetailParams, options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>('/assetcatalog/table/detail', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询数据表 数据表
 * @url /assetcatalog/table/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postAssetcatalogTablePageQuery<
  R = API.ResponseDataPageDataDataProjectDomainTableListVo,
  T = API.ResponseDataPageDataDataProjectDomainTableListVo,
>(body: API.DataProjectDomainTablePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/assetcatalog/table/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询数据表下拉框 数据表
 * @url /assetcatalog/table/queryList
 * @method GET
 * <AUTHOR>
 */
export function getAssetcatalogTableQueryList<
  R = API.ResponseDataListDataProjectDomainTableSimpleListVo,
  T = API.ResponseDataListDataProjectDomainTableSimpleListVo,
>(
  params: API.getAssetcatalogTableQueryListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/assetcatalog/table/queryList', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
