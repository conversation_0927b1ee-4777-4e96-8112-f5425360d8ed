import { basic as request } from '@/api/request';

/**
 * @description 所有应用，未分页
 * @url /monitor/app/list
 * @method GET
 * <AUTHOR>
 */
export function getMonitorAppList<R = API.MonitorAppDto, T = API.MonitorAppDto>(
  params: API.getMonitorAppListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/monitor/app/list', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
