import { basic as request } from '@/api/request';

/**
 * @description 批量删除脱敏模板 脱敏模板
 * @url /dataSecurity/desenTemplate/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除脱敏模板 脱敏模板
 * @url /dataSecurity/desenTemplate/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 脱敏测试 脱敏模板
 * @url /dataSecurity/desenTemplate/desenTest
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateDesenTest<
  R = API.ResponseDataString,
  T = API.ResponseDataString,
>(body: API.DesensitizationTemplateDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/desenTest', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 脱敏模板详情 脱敏模板
 * @url /dataSecurity/desenTemplate/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateDetail<
  R = API.ResponseDataDesensitizationTemplateDto,
  T = API.ResponseDataDesensitizationTemplateDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 脱敏模板下拉 脱敏模板
 * @url /dataSecurity/desenTemplate/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateOptions<
  R = API.ResponseDataListOptionVoStringLong,
  T = API.ResponseDataListOptionVoStringLong,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataSecurity/desenTemplate/options',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description 脱敏模板分页列表 脱敏模板
 * @url /dataSecurity/desenTemplate/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplatePageList<
  R = API.ResponseDataPageDesensitizationTemplateListVo,
  T = API.ResponseDataPageDesensitizationTemplateListVo,
>(body: API.DesensitizationTemplateQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加脱敏模板 脱敏模板
 * @url /dataSecurity/desenTemplate/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DesensitizationTemplateDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改脱敏模板 脱敏模板
 * @url /dataSecurity/desenTemplate/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DesensitizationTemplateDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/desenTemplate/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改脱敏模板状态 脱敏模板
 * @url /dataSecurity/desenTemplate/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDesenTemplateUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(
  params: API.postDataSecurityDesenTemplateUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataSecurity/desenTemplate/updStatus',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
