import { basic as request } from '@/api/request';

/**
 * @description 批量删除安全密级 安全密级
 * @url /dataSecurity/securityClass/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除安全密级 安全密级
 * @url /dataSecurity/securityClass/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 安全密级下拉 安全密级
 * @url /dataSecurity/securityClass/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassOptions<
  R = API.ResponseDataListSecurityClassListVo,
  T = API.ResponseDataListSecurityClassListVo,
>(body: API.SecurityClassQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/options', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 安全密级分页列表 安全密级
 * @url /dataSecurity/securityClass/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassPageList<
  R = API.ResponseDataPageSecurityClassListVo,
  T = API.ResponseDataPageSecurityClassListVo,
>(body: API.SecurityClassQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加安全密级 安全密级
 * @url /dataSecurity/securityClass/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.SecurityClassDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改安全密级 安全密级
 * @url /dataSecurity/securityClass/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecuritySecurityClassUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.SecurityClassDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataSecurity/securityClass/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
