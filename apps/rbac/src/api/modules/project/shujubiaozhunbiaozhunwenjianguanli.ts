import { basic as request } from '@/api/request';

/**
 * @description 批量删除标准文件 标准文件
 * @url /dataStandard/standardFile/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/standardFile/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 批量下载标准文件 标准文件
 * @url /dataStandard/standardFile/batchDownload
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileBatchDownload<
  R = API.ResponseData,
  T = API.ResponseData,
>(body: API.StandardFileDownLoadQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/standardFile/batchDownload', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除标准文件 标准文件
 * @url /dataStandard/standardFile/del
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/standardFile/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 标准文件分类下拉 标准文件
 * @url /dataStandard/standardFile/fileCategoryOption
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileFileCategoryOption<
  R = API.ResponseDataListString,
  T = API.ResponseDataListString,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataStandard/standardFile/fileCategoryOption',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description 标准文件管理分页列表 标准文件
 * @url /dataStandard/standardFile/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFilePageList<
  R = API.ResponseDataPageStandardFileListVo,
  T = API.ResponseDataPageStandardFileListVo,
>(body: API.StandardFileQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/standardFile/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加标准文件 标准文件
 * @url /dataStandard/standardFile/save
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.StandardFileDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/standardFile/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 标准文件管理分页列表 标准文件
 * @url /dataStandard/standardFile/statusStat
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardStandardFileStatusStat<
  R = API.ResponseDataListStandardFileStatVo,
  T = API.ResponseDataListStandardFileStatVo,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataStandard/standardFile/statusStat',
    {},
    {
      ...(options || {}),
    },
  );
}
