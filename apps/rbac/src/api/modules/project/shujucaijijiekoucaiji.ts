import { basic as request } from '@/api/request';

/**
 * @description 添加接口数据集
 * @url /dataIntegrationMethodServer/add
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerAdd<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.ApiSetAddParams, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/add', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据查询创建
 * @url /dataIntegrationMethodServer/createPreview
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerCreatePreview<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.DataPreviewDO, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/createPreview', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除接口数据集
 * @url /dataIntegrationMethodServer/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerDelete<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/delete', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 通过id,查询对应接口数据集
 * @url /dataIntegrationMethodServer/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerDetail<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 接口文档下载
 * @url /dataIntegrationMethodServer/download
 * @method GET
 * <AUTHOR>
 */
export function getDataIntegrationMethodServerDownload<
  R = Record<string, any>,
  T = Record<string, any>,
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>('/dataIntegrationMethodServer/download', {
    ...(options || {}),
  });
}

/**
 * @description 编辑接口数据集
 * @url /dataIntegrationMethodServer/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerEdit<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.ApiSetUpdateParams, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/edit', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 下载Excel模板
 * @url /dataIntegrationMethodServer/excelTemplate
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerExcelTemplate<R = any, T = any>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataIntegrationMethodServer/excelTemplate', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改任务状态 修改任务状态
 * @url /dataIntegrationMethodServer/mission
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerMission<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.MissionEditDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/mission', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询 分页查询
 * @url /dataIntegrationMethodServer/page
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerPage<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject,
>(body: API.DataIntegrationMethodSqlPageDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 下载文本模板
 * @url /dataIntegrationMethodServer/textTemplate
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerTextTemplate<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodServer/textTemplate', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 生成clientSecret接口
 * @url /dataIntegrationMethodServer/updateClientSecret
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodServerUpdateClientSecret<
  R = Record<string, any>,
  T = Record<string, any>,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataIntegrationMethodServer/updateClientSecret',
    {},
    {
      ...(options || {}),
    },
  );
}
