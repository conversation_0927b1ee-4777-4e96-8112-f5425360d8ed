import { basic as request } from '@/api/request';

/**
 * @description 加密节点-获取已配置数据资产配置 加密节点-获取已配置数据资产配置
 * @url /dataWorkflow/encryptTask/find
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowEncryptTaskFind<
  R = API.ResponseDataEncryptTaskDto,
  T = API.ResponseDataEncryptTaskDto,
>(body: API.EncryptTaskId, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/encryptTask/find', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 加密节点-数据资产配置 加密节点-数据资产配置
 * @url /dataWorkflow/encryptTask/save
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowEncryptTaskSave<
  R = API.ResponseDataEncryptTaskScript,
  T = API.ResponseDataEncryptTaskScript,
>(body: API.EncryptTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/encryptTask/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 节点-Worer分组选项 Worer分组选项
 * @url /dataWorkflow/findAllWorkGroup
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowFindAllWorkGroup<
  R = API.ResponseDataListDsWorkerGroup,
  T = API.ResponseDataListDsWorkerGroup,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dataWorkflow/findAllWorkGroup',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description DS数据源下拉框选项 根据数据源类型查询
 * @url /dataWorkflow/findDsDataSource
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowFindDsDataSource<
  R = API.ResponseDataListDsDataSourceSimple,
  T = API.ResponseDataListDsDataSourceSimple,
>(body: API.DsDataSourceType, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/findDsDataSource', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 工作流详情 工作流详情
 * @url /dataWorkflow/findOne
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowFindOne<
  R = API.ResponseDataDataWorkflowDefineSave,
  T = API.ResponseDataDataWorkflowDefineSave,
>(body: API.DataWorkflowId, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/findOne', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 创建DS项目 返回DS项目projectCode
 * @url /dataWorkflow/findOrCreateDsProject
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowFindOrCreateDsProject<
  R = API.ResponseDataLong,
  T = API.ResponseDataLong,
>(body: API.DataProjectDomainForDs, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/findOrCreateDsProject', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务日志查询 任务日志查询
 * @url /dataWorkflow/findTaskRunLog
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowFindTaskRunLog<
  R = API.ResponseDataLogQueryResp,
  T = API.ResponseDataLogQueryResp,
>(body: API.DsTaskRunLogParams, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/findTaskRunLog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 生成taskCode 生成taskCode
 * @url /dataWorkflow/generateTaskCode
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowGenerateTaskCode<
  R = API.ResponseDataLong,
  T = API.ResponseDataLong,
>(body: API.DataProjectCode, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/generateTaskCode', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 增量节点配置查询 增量节点配置查询
 * @url /dataWorkflow/incrTask/find
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowIncrTaskFind<
  R = API.ResponseDataDataWorkflowIncrTaskDto,
  T = API.ResponseDataDataWorkflowIncrTaskDto,
>(body: API.IncrTaskId, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/incrTask/find', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 增量节点配置保存 包括新增和编辑
 * @url /dataWorkflow/incrTask/save
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowIncrTaskSave<
  R = API.ResponseDataIncrTaskScript,
  T = API.ResponseDataIncrTaskScript,
>(body: API.DataWorkflowIncrTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/incrTask/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 工作流实例列表 分页查询
 * @url /dataWorkflow/instance/page
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowInstancePage<
  R = API.ResponseDataPageDataDataWorkflowInstance,
  T = API.ResponseDataPageDataDataWorkflowInstance,
>(body: API.DataWorkflowInstancePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/instance/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 下线工作流 下线工作流
 * @url /dataWorkflow/offline
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowOffline<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWorkflowId,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWorkflow/offline', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 上线工作流 上线工作流
 * @url /dataWorkflow/online
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowOnline<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWorkflowId,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWorkflow/online', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 工作流列表 分页查询
 * @url /dataWorkflow/page
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowPage<
  R = API.ResponseDataPageDataDataWorkflowDefineRow,
  T = API.ResponseDataPageDataDataWorkflowDefineRow,
>(body: API.DataWorkflowDefinePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 保存工作流 新增和编辑保存
 * @url /dataWorkflow/saveDefine
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowSaveDefine<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWorkflowDefineSave,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWorkflow/saveDefine', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询调度 查询调度
 * @url /dataWorkflow/schedule/find
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowScheduleFind<
  R = API.ResponseDataDataWorkflowPlanDto,
  T = API.ResponseDataDataWorkflowPlanDto,
>(body: API.DataWorkflowId, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/schedule/find', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 保存调度 保存调度
 * @url /dataWorkflow/schedule/save
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowScheduleSave<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject,
>(body: API.DataWorkflowPlanDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/schedule/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 停止工作流 停止工作流
 * @url /dataWorkflow/shutdown
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowShutdown<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWorkflowId,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWorkflow/shutdown', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 启动工作流 启动工作流
 * @url /dataWorkflow/startup
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowStartup<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWorkflowId,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWorkflow/startup', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务实例列表 分页查询
 * @url /dataWorkflow/taskInstance/page
 * @method POST
 * <AUTHOR>
 */
export function postDataWorkflowTaskInstancePage<
  R = API.ResponseDataPageDataDsTaskInstance,
  T = API.ResponseDataPageDataDsTaskInstance,
>(body: API.DsTaskInstancePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWorkflow/taskInstance/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
