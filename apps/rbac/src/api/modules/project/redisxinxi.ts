import { basic as request } from '@/api/request';

/**
 * @description 逻辑删除 redis 信息 (by id)
 * @url /info/redis/del
 * @method POST
 * <AUTHOR>
 */
export function postInfoRedisDel<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoRedisDelParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/redis/del',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取 redis 信息
 * @url /info/redis/get
 * @method POST
 * <AUTHOR>
 */
export function postInfoRedisGet<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoRedisGetParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/redis/get',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取全部 redis 信息
 * @url /info/redis/getAll
 * @method POST
 * <AUTHOR>
 */
export function postInfoRedisGetAll<R = Record<string, any>, T = Record<string, any>>(
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/redis/getAll',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description [添加/更新] redis 信息
 * @url /info/redis/upsert
 * @method POST
 * <AUTHOR>
 */
export function postInfoRedisUpsert<R = Record<string, any>, T = Record<string, any>>(
  body: API.RedisInfoSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/info/redis/upsert', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
