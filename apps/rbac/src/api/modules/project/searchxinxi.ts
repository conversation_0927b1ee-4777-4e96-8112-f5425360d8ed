import { basic as request } from '@/api/request';

/**
 * @description 逻辑删除 Search 信息 (by id)
 * @url /info/es/del
 * @method POST
 * <AUTHOR>
 */
export function postInfoEsDel<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoEsDelParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/es/del',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取对应 Search 节点信息
 * @url /info/es/get
 * @method POST
 * <AUTHOR>
 */
export function postInfoEsGet<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoEsGetParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/es/get',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 获取全部 Search 节点信息
 * @url /info/es/getAll
 * @method POST
 * <AUTHOR>
 */
export function postInfoEsGetAll<R = Record<string, any>, T = Record<string, any>>(
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/info/es/getAll',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description [添加/更新] Search 信息
 * @url /info/es/upsert
 * @method POST
 * <AUTHOR>
 */
export function postInfoEsUpsert<R = Record<string, any>, T = Record<string, any>>(
  body: API.SearchInfoSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/info/es/upsert', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
