import { basic as request } from '@/api/request';

/**
 * @description 资产权限查询 资产权限管理
 * @url /dataArchitecture/dataAssetPermission/list
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataAssetPermissionList<
  R = API.ResponseDataListDataAssetPermissionListVo,
  T = API.ResponseDataListDataAssetPermissionListVo,
>(body: API.DataAssetPermissionDetailDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataAssetPermission/list', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 资产权限变更记录查询 资产权限管理历史记录
 * @url /dataArchitecture/dataAssetPermission/savePermission
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataAssetPermissionSavePermission<
  R = API.ResponseDataListDataAssetPermissionHistory,
  T = API.ResponseDataListDataAssetPermissionHistory,
>(
  params: API.getDataArchitectureDataAssetPermissionSavePermissionParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataAssetPermission/savePermission', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 添加资产权限关系 资产权限管理
 * @url /dataArchitecture/dataAssetPermission/savePermission
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataAssetPermissionSavePermission<
  R = API.ResponseData,
  T = API.ResponseData,
>(body: API.DataAssetPermissionDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataAssetPermission/savePermission', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除数据板块 数据板块
 * @url /dataArchitecture/dataProject/deleted
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDeleted<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDataArchitectureDataProjectDeletedParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataArchitecture/dataProject/deleted',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 查询数据板块下拉框(根据当前用户权限过滤) 数据板块
 * @url /dataArchitecture/dataProject/list
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectList<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProject/list', {
    ...(options || {}),
  });
}

/**
 * @description 查询数据板块下所有业务负责人 数据板块
 * @url /dataArchitecture/dataProject/listBusiness
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectListBusiness<
  R = API.ResponseDataListPubUserDto,
  T = API.ResponseDataListPubUserDto,
>(
  params: API.getDataArchitectureDataProjectListBusinessParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProject/listBusiness', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询数据板块(根据当前用户权限过滤) 数据板块
 * @url /dataArchitecture/dataProject/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectPageQuery<
  R = API.ResponseDataPageDataDataProjectVo,
  T = API.ResponseDataPageDataDataProjectVo,
>(body: API.PageBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProject/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 新增编辑数据板块 数据板块
 * @url /dataArchitecture/dataProject/save
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectSave<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataProjectDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataArchitecture/dataProject/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除数据主题域 数据主题域
 * @url /dataArchitecture/dataProjectDomain/deleted
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainDeleted<
  R = API.ResponseData,
  T = API.ResponseData,
>(
  params: API.postDataArchitectureDataProjectDomainDeletedParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataArchitecture/dataProjectDomain/deleted',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 分页查询数据主题域 数据主题域
 * @url /dataArchitecture/dataProjectDomain/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainPageQuery<
  R = API.ResponseDataPageDataDataProjectDomainVo,
  T = API.ResponseDataPageDataDataProjectDomainVo,
>(body: API.DataProjectDomainPage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomain/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询所有数据主题域 数据主题域
 * @url /dataArchitecture/dataProjectDomain/queryList
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainQueryList<
  R = API.ResponseDataListDataProjectDomain,
  T = API.ResponseDataListDataProjectDomain,
>(
  params: API.getDataArchitectureDataProjectDomainQueryListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomain/queryList', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 查询树结构数据主题域 数据主题域
 * @url /dataArchitecture/dataProjectDomain/queryTree
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainQueryTree<
  R = API.ResponseDataPageDataTreeVo,
  T = API.ResponseDataPageDataTreeVo,
>(
  params: API.getDataArchitectureDataProjectDomainQueryTreeParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomain/queryTree', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 新增编辑数据主题域 数据主题域
 * @url /dataArchitecture/dataProjectDomain/save
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainSave<
  R = API.ResponseData,
  T = API.ResponseData,
>(body: API.DataProjectDomainDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomain/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 取消业务实体关联表 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/cancelTable
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityCancelTable<
  R = API.ResponseData,
  T = API.ResponseData,
>(
  params: API.postDataArchitectureDataProjectDomainEntityCancelTableParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataArchitecture/dataProjectDomainEntity/cancelTable',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 上线或下线数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/change
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityChange<
  R = API.ResponseData,
  T = API.ResponseData,
>(
  params: API.postDataArchitectureDataProjectDomainEntityChangeParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataArchitecture/dataProjectDomainEntity/change',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 删除数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/deleted
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityDeleted<
  R = API.ResponseData,
  T = API.ResponseData,
>(
  params: API.postDataArchitectureDataProjectDomainEntityDeletedParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataArchitecture/dataProjectDomainEntity/deleted',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 查询指定版本数据主题域业务实体历史记录详情 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/detail
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainEntityDetail<
  R = API.ResponseDataDataProjectDomainEntityHistoryVo,
  T = API.ResponseDataDataProjectDomainEntityHistoryVo,
>(
  params: API.getDataArchitectureDataProjectDomainEntityDetailParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomainEntity/detail', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 查找指定id数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/findOne
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainEntityFindOne<
  R = API.ResponseDataDataProjectDomainEntityVo,
  T = API.ResponseDataDataProjectDomainEntityVo,
>(
  params: API.getDataArchitectureDataProjectDomainEntityFindOneParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomainEntity/findOne', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 查找指定来源的数据主题域业务实体的关联关系 数据主题域实体关联关系
 * @url /dataArchitecture/dataProjectDomainEntity/getRelation
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainEntityGetRelation<
  R = API.ResponseDataListDataProjectDomainEntityRelationTreeVo,
  T = API.ResponseDataListDataProjectDomainEntityRelationTreeVo,
>(
  params: API.getDataArchitectureDataProjectDomainEntityGetRelationParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomainEntity/getRelation', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityPageQuery<
  R = API.ResponseDataPageDataDataProjectDomainEntityListVo,
  T = API.ResponseDataPageDataDataProjectDomainEntityListVo,
>(body: API.DataProjectDomainEntityPage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomainEntity/pageQuery', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询数据主题域业务实体相关逻辑表 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/pageQueryTable
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityPageQueryTable<
  R = API.ResponseDataPageDataDataProjectDomainEntityTableListVo,
  T = API.ResponseDataPageDataDataProjectDomainEntityTableListVo,
>(body: API.DataProjectDomainEntityTablePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomainEntity/pageQueryTable', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询树结构数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/queryTree
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainEntityQueryTree<
  R = API.ResponseDataPageDataTreeVo,
  T = API.ResponseDataPageDataTreeVo,
>(
  params: API.getDataArchitectureDataProjectDomainEntityQueryTreeParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomainEntity/queryTree', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 新增编辑数据主题域业务实体 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/save
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntitySave<
  R = API.ResponseData,
  T = API.ResponseData,
>(body: API.DataProjectDomainEntityDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomainEntity/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加业务实体关联表 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/saveTable
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntitySaveTable<
  R = API.ResponseData,
  T = API.ResponseData,
>(body: API.DataProjectDomainEntityTableDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomainEntity/saveTable', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询数据主题域业务实体版本信息 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/version
 * @method POST
 * <AUTHOR>
 */
export function postDataArchitectureDataProjectDomainEntityVersion<
  R = API.ResponseDataPageDataDataProjectDomainEntityHistoryListVo,
  T = API.ResponseDataPageDataDataProjectDomainEntityHistoryListVo,
>(
  body: API.DataProjectDomainEntityVerisonPage,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataArchitecture/dataProjectDomainEntity/version', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询指定数据主题域业务实体的所有版本 数据主题域实体
 * @url /dataArchitecture/dataProjectDomainEntity/versionList
 * @method GET
 * <AUTHOR>
 */
export function getDataArchitectureDataProjectDomainEntityVersionList<
  R = API.ResponseData,
  T = API.ResponseData,
>(
  params: API.getDataArchitectureDataProjectDomainEntityVersionListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataArchitecture/dataProjectDomainEntity/versionList', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
