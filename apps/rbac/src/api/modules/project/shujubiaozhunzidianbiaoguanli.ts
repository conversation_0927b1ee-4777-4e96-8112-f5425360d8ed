import { basic as request } from '@/api/request';

/**
 * @description 批量删除字典 字典管理
 * @url /dataStandard/dictionary/dict/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/batchDel', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除字典 字典管理
 * @url /dataStandard/dictionary/dict/del
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典详情 字典管理
 * @url /dataStandard/dictionary/dict/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictDetail<
  R = API.ResponseDataDictionaryDto,
  T = API.ResponseDataDictionaryDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典状态统计 字典管理
 * @url /dataStandard/dictionary/dict/dictStatusStat
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictDictStatusStat<
  R = API.ResponseDataListDiscStatusStatVo,
  T = API.ResponseDataListDiscStatusStatVo,
>(body: API.DictionaryQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/dictStatusStat', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典信息树 字典管理
 * @url /dataStandard/dictionary/dict/dictTree
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictDictTree<
  R = API.ResponseDataListDirectoryDto,
  T = API.ResponseDataListDirectoryDto,
>(body: API.DictionaryQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/dictTree', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典管理数据下载 字典管理
 * @url /dataStandard/dictionary/dict/download
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictDownload<R = API.ResponseData, T = API.ResponseData>(
  body: API.DictionaryQuery,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/download', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典管理数据导入模板 字典管理
 * @url /dataStandard/dictionary/dict/downloadImportTemplate
 * @method GET
 * <AUTHOR>
 */
export function getDataStandardDictionaryDictDownloadImportTemplate<R = any, T = any>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/dataStandard/dictionary/dict/downloadImportTemplate', {
    ...(options || {}),
  });
}

/**
 * @description 字典管理数据导入 字典管理
 * @url /dataStandard/dictionary/dict/import
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictImport<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDataStandardDictionaryDictImportParams,
  body: Record<string, unknown>,
  file?: File,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach(f => formData.append(ele, f || ''));
        }
        else {
          formData.append(ele, JSON.stringify(item));
        }
      }
      else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>('/dataStandard/dictionary/dict/import', formData, {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 字典管理分页列表 字典管理
 * @url /dataStandard/dictionary/dict/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictPageList<
  R = API.ResponseDataPageDataStandardDictListVo,
  T = API.ResponseDataPageDataStandardDictListVo,
>(body: API.DictionaryQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 保存字典到草稿 字典管理
 * @url /dataStandard/dictionary/dict/saveDraft
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictSaveDraft<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DictionaryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/saveDraft', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加或修改字典 字典管理
 * @url /dataStandard/dictionary/dict/saveOrUpd
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictSaveOrUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DictionaryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/dict/saveOrUpd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 字典 上线/下线 字典管理
 * @url /dataStandard/dictionary/dict/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDictUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(
  params: API.postDataStandardDictionaryDictUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataStandard/dictionary/dict/updStatus',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 删除目录 目录
 * @url /dataStandard/dictionary/directory/del
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDirectoryDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/directory/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加目录 目录
 * @url /dataStandard/dictionary/directory/save
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDirectorySave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DirectoryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/directory/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改目录 目录
 * @url /dataStandard/dictionary/directory/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDirectoryUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DirectoryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataStandard/dictionary/directory/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查看目录树 目录
 * @url /dataStandard/dictionary/directoryTree
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardDictionaryDirectoryTree<
  R = API.ResponseDataListDirectoryDto,
  T = API.ResponseDataListDirectoryDto,
>(
  params: API.postDataStandardDictionaryDirectoryTreeParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dataStandard/dictionary/directoryTree',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
