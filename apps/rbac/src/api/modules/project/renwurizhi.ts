import { basic as request } from '@/api/request';

/**
 * @description 删除任务 质量任务
 * @url /dataQuality/task/del
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskDel<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataQuality/task/del', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务详情 质量任务
 * @url /dataQuality/task/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskDetail<
  R = API.ResponseDataDataQualityTaskDto,
  T = API.ResponseDataDataQualityTaskDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 质量任务分页列表 质量任务
 * @url /dataQuality/task/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskPageList<
  R = API.ResponseDataPageDataQualityTaskListVo,
  T = API.ResponseDataPageDataQualityTaskListVo,
>(body: API.DataQualityTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 质量报告 质量任务
 * @url /dataQuality/task/report
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskReport<
  R = API.ResponseDataTaskResultReportVo,
  T = API.ResponseDataTaskResultReportVo,
>(body: API.DataQualityTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/report', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加任务 质量任务
 * @url /dataQuality/task/save
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskSave<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.DataQualityTaskDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataQuality/task/save', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 添加任务到草稿 质量任务
 * @url /dataQuality/task/saveDraft
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskSaveDraft<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean,
>(body: API.DataQualityTaskDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/saveDraft', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据表下拉 质量任务
 * @url /dataQuality/task/tablePublishOption
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskTablePublishOption<
  R = API.ResponseDataListTablePublishVo,
  T = API.ResponseDataListTablePublishVo,
>(body: API.DataQualityTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/tablePublishOption', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务日志 质量任务
 * @url /dataQuality/task/taskLog
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskTaskLog<
  R = API.ResponseDataQualityTaskLogVo,
  T = API.ResponseDataQualityTaskLogVo,
>(body: API.DataQualityTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/taskLog', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 任务日志分页列表 任务日志
 * @url /dataQuality/task/taskLog/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskTaskLogPageList<
  R = API.ResponseDataPageQualityTaskLogListVo,
  T = API.ResponseDataPageQualityTaskLogListVo,
>(body: API.DataQualityTaskQuery, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataQuality/task/taskLog/pageList', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 修改任务 质量任务
 * @url /dataQuality/task/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataQualityTaskUpd<R = API.ResponseDataBoolean, T = API.ResponseDataBoolean>(
  body: API.DataQualityTaskDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataQuality/task/upd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
