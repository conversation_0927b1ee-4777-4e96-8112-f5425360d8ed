import { basic as request } from '@/api/request';

/**
 * @description 所有字段类型 所有字段类型
 * @url /dictData/allDictDataColumnType
 * @method POST
 * <AUTHOR>
 */
export function postDictDataAllDictDataColumnType<
  R = API.ResponseDataListDictDataColumnType,
  T = API.ResponseDataListDictDataColumnType,
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    '/dictData/allDictDataColumnType',
    {},
    {
      ...(options || {}),
    },
  );
}

/**
 * @description 查询redis指定值 查询redis指定值
 * @url /dictData/check
 * @method POST
 * <AUTHOR>
 */
export function postDictDataCheck<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDictDataCheckParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dictData/check',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 删除redis指定值 删除redis指定值
 * @url /dictData/del
 * @method POST
 * <AUTHOR>
 */
export function postDictDataDel<R = API.ResponseData, T = API.ResponseData>(
  params: API.postDictDataDelParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dictData/del',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 根据dictCode查询字典列表 字典列表
 * @url /dictData/findByDictCode
 * @method POST
 * <AUTHOR>
 */
export function postDictDataFindByDictCode<
  R = API.ResponseDataListDictData,
  T = API.ResponseDataListDictData,
>(body: API.DictDataParams, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dictData/findByDictCode', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 刷新Flink_DB 刷新Flink_DB
 * @url /dictData/refreshFlinkDb
 * @method POST
 * <AUTHOR>
 */
export function postDictDataRefreshFlinkDb<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/dictData/refreshFlinkDb',
    {},
    {
      ...(options || {}),
    },
  );
}
