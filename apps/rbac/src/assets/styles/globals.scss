// 页面布局 CSS 变量
:root {
  // 这是一个复合变量
  // 当页宽模式为 adaption-min-width 时，它代表 最小宽度
  // 当页宽模式为 center 时，它代表 固定宽度
  // 当页宽模式为 center-max-width 时，它代表 最大宽度
  --g-app-width: #{$g-app-width};

  // 头部宽度（默认自适应宽度，可固定宽度，固定宽度后为居中显示）
  --g-header-width: #{$g-header-width};

  // 头部高度
  --g-header-height: 56px;

  // 侧边栏宽度
  --g-main-sidebar-width: 63px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 64px;

  // 侧边栏 Logo 区域高度
  --g-sidebar-logo-height: 50px;

  // 标签栏高度
  --g-tabbar-height: 48px;

  // 工具栏高度
  --g-toolbar-height: 45px;

  // 标签页最大最小宽度，两个宽度为同一数值时，则为固定宽度，反之将宽度设置为 unset 则为自适应
  --g-tabbar-tab-max-width: 150px;
  --g-tabbar-tab-min-width: 150px;
}

// 明暗模式 CSS 变量
/* stylelint-disable-next-line no-duplicate-selectors */
:root {
  color-scheme: light;

  --g-box-shadow-color: rgb(0 0 0 / 12%);

  &::view-transition-old(root),
  &::view-transition-new(root) {
    mix-blend-mode: normal;
    animation: none;
  }

  &::view-transition-old(root) {
    z-index: 1;
  }

  &::view-transition-new(root) {
    z-index: 9999;
  }

  &.dark {
    color-scheme: dark;

    --g-box-shadow-color: rgb(0 0 0 / 72%);

    &::view-transition-old(root) {
      z-index: 9999;
    }

    &::view-transition-new(root) {
      z-index: 1;
    }
  }
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

html,
body {
  height: 100%;
}

body {
  box-sizing: border-box;
  margin: 0;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: var(--g-container-bg);
  -webkit-tap-highlight-color: transparent;

  &.overflow-hidden {
    overflow: hidden;
  }
}

* {
  box-sizing: inherit;
}

// 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width]
[data-fixed-calc-width] {
  position: fixed;
  right: 0;
  left: 50%;
}

[data-app-width-mode="adaption"],
[data-app-width-mode="adaption-min-width"] {
  [data-fixed-calc-width] {
    width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
  }
}

[data-app-width-mode="center"],
[data-app-width-mode="center-max-width"] {
  [data-fixed-calc-width] {
    width: calc(var(--g-app-width) - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
  }

  @media screen and (max-width: $g-app-width) {
    [data-fixed-calc-width] {
      width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
      transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
    }
  }
}

[data-mode="mobile"] {
  [data-fixed-calc-width] {
    width: 100% !important;
    transform: translateX(-50%) !important;
  }
}

// textarea 字体跟随系统
textarea {
  font-family: inherit;
}

/* Overrides Floating Vue */
.v-popper--theme-dropdown,
.v-popper--theme-tooltip {
  --at-apply: inline-flex;
}

.v-popper--theme-dropdown .v-popper__inner,
.v-popper--theme-tooltip .v-popper__inner {
  --at-apply: bg-white  dark:bg-dark-9 text-dark dark:text-white rounded shadow ring-1 ring-gray-200 dark:ring-gray-800 border border-solid border-stone/20 text-xs font-normal;

  box-shadow: 0 6px 30px rgb(0 0 0 / 10%);
}

.v-popper--theme-tooltip .v-popper__arrow-inner,
.v-popper--theme-dropdown .v-popper__arrow-inner {
  --at-apply: border-white dark:border-stone-8;
}

.v-popper--theme-tooltip .v-popper__arrow-outer,
.v-popper--theme-dropdown .v-popper__arrow-outer {
  --at-apply: border-stone/20;
}

.v-popper--theme-tooltip.v-popper--shown,
.v-popper--theme-tooltip.v-popper--shown * {
  transition: none !important;
}

[data-overlayscrollbars-contents] {
  overscroll-behavior: contain;
}

// medium-zoom
.medium-zoom-overlay,
.medium-zoom-image {
  z-index: 3000;
}

// 目前为rbac专用配置，添加自定义样式，在模板中移除
[data-theme="classic"] {
  #app-main {
    // FIXME: 临时解决方案 --------
    // background-image: url("@/assets/images/slider-bg.png");
    // background-repeat: no-repeat;
    // background-position: left bottom;
    // background-size: 450px 89%;

    .sub-sidebar-container {
      background-color: transparent;
    }

    & > header {
      background-image: url("@/assets/images/header-bg.png");
      background-repeat: no-repeat;
      background-position: left bottom;
      background-size: cover;

      .menu {
        background-color: transparent !important;
      }

      .header-container {
        .tools {
          & > span {
            background-color: #4173f9;
          }
        }
      }
    }

    .page-container {
      padding-left: 0;
      overflow-y: auto;
    }
  }

  .wrapper .sidebar-container {
    box-shadow: none !important;
  }

  .wrapper .main-container {
    box-shadow: none !important;
  }
}

/* FIXME: Over rides @pubinfo/pro-components */
.pro-table {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-card .ant-card-body {
    padding: 16px;
  }

  .ant-card .ant-card-head {
    min-height: 48px;
    padding: 0 16px;
  }
}

.form-group-title {
  display: flex;
  align-items: center;
  padding: 16px 0;
  margin: 14px 0 20px;
  border-bottom: 1px solid #e7e7e7;

  >img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  >span {
    font-size: 18px;
    font-weight: bold;
    line-height: 21px;
    color: #161e5d;
  }

  .form-group-title-extra {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: #ffa800;
  }
}

.button-group {
  display: flex;
  justify-content: flex-end;

  .ant-btn + .ant-btn,
  .ant-btn + .ant-upload-wrapper,
  .ant-upload-wrapper + .ant-btn {
    margin-left: 16px;
  }

  .ant-btn {
    display: inline-flex;
    align-items: center;

    i {
      font-size: 16px;
    }
  }
}

.warning-button {
  color: #fff;
  background-color: #fcc250;
  border-color: #fcc250;

  &:not(:disabled):hover {
    position: relative;
    color: #fff;
    background-color: #fcc250;
    border-color: #fcc250;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 100%;
      content: "";
      background-color: rgb(0 0 0 / 20%);
    }
  }
}

.drawer-level-two {
  z-index: 1001;
}

.table-operation-button-group {
  display: flex;

  .ant-btn-link {
    height: fit-content;
    padding: 0 16px;
    line-height: 20px;

    &:disabled {
      color: #1677ff;

      i,
      span {
        opacity: 0.4;
      }
    }

    &:first-child {
      padding-left: 12px;
    }

    &:last-child {
      padding-right: 12px;
    }

    &:not(:last-child) {
      position: relative;

      &::before {
        position: absolute;
        right: 0;
        height: 20px;
        content: "";
        border-right: 1px solid #e7e7e7;
      }
    }
  }
}

.table-operation-button-danger {
  color: #f05f5f !important;

  &:hover,
  &:active,
  &:focus {
    color: #f05f5f !important;
  }
}

.table-operation-button-group__menu {
  padding: 14px !important;

  .ant-btn-link {
    height: fit-content;
    padding: 0;
    line-height: 20px;
  }

  .ant-dropdown-menu-item {
    padding: 8px 6px !important;
  }
}
