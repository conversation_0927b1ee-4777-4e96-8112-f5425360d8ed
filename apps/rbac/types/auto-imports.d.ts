/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const Modal: typeof import('ant-design-vue')['Modal']
  const afterAll: typeof import('vitest')['afterAll']
  const afterEach: typeof import('vitest')['afterEach']
  const assert: typeof import('vitest')['assert']
  const beforeAll: typeof import('vitest')['beforeAll']
  const beforeEach: typeof import('vitest')['beforeEach']
  const chai: typeof import('vitest')['chai']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const deleteDictItemDeleteById: typeof import('../src/api/modules/configData/zidianfuwu')['deleteDictItemDeleteById']
  const deleteDictTypeDeleteById: typeof import('../src/api/modules/configData/zidianfuwu')['deleteDictTypeDeleteById']
  const deleteGroupDeleteById: typeof import('../src/api/modules/assist/qunzujiekou')['deleteGroupDeleteById']
  const deleteRbacPostDeleteById: typeof import('../src/api/modules/rbac/gangweijiekou')['deleteRbacPostDeleteById']
  const deleteRbacPubDataPermissionsDelete: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['deleteRbacPubDataPermissionsDelete']
  const deleteRbacPubRoleGroupDelete: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['deleteRbacPubRoleGroupDelete']
  const describe: typeof import('vitest')['describe']
  const effectScope: typeof import('vue')['effectScope']
  const expect: typeof import('vitest')['expect']
  const getApiAppApiFindList: typeof import('../src/api/modules/project/yingyongApipeizhi')['getApiAppApiFindList']
  const getApiAppApiFindListYpz: typeof import('../src/api/modules/project/yingyongApipeizhi')['getApiAppApiFindListYpz']
  const getApiAppApiFindParam: typeof import('../src/api/modules/project/yingyongApipeizhi')['getApiAppApiFindParam']
  const getApiDataCheckUrl: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataCheckUrl']
  const getApiDataFind: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataFind']
  const getApiDataFindAllByApiId: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataFindAllByApiId']
  const getApiDataFindByParentIdAndVersion: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataFindByParentIdAndVersion']
  const getApiDataFindVersionList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataFindVersionList']
  const getApiDataGetApiDomainList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataGetApiDomainList']
  const getApiDataGetApiInOut: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataGetApiInOut']
  const getApiDataGetApiList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataGetApiList']
  const getApiDataGetApiProject: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataGetApiProject']
  const getApiDataGetTableMsg: typeof import('../src/api/modules/project/apIguanlishujukaifang')['getApiDataGetTableMsg']
  const getApiUserCreateClient: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['getApiUserCreateClient']
  const getAssetcatalogQueueDataType: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogQueueDataType']
  const getAssetcatalogQueueDetail: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogQueueDetail']
  const getAssetcatalogQueueQueryList: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogQueueQueryList']
  const getAssetcatalogQueueSource: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogQueueSource']
  const getAssetcatalogTableDetail: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogTableDetail']
  const getAssetcatalogTableQueryList: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['getAssetcatalogTableQueryList']
  const getAuthCheckCacheToken: typeof import('../src/api/modules/auth/renzhengfuwu')['getAuthCheckCacheToken']
  const getAuthLoginKey: typeof import('../src/api/modules/auth/renzhengfuwu')['getAuthLoginKey']
  const getAuthLoginValidCode: typeof import('../src/api/modules/auth/renzhengfuwu')['getAuthLoginValidCode']
  const getBlackWhiteListDeleteById: typeof import('../src/api/modules/configData/heibaimingdanfuwu')['getBlackWhiteListDeleteById']
  const getBlackWhiteListGetBlackWhiteListById: typeof import('../src/api/modules/configData/heibaimingdanfuwu')['getBlackWhiteListGetBlackWhiteListById']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDataArchitectureDataAssetPermissionSavePermission: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataAssetPermissionSavePermission']
  const getDataArchitectureDataProjectDomainEntityDetail: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainEntityDetail']
  const getDataArchitectureDataProjectDomainEntityFindOne: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainEntityFindOne']
  const getDataArchitectureDataProjectDomainEntityGetRelation: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainEntityGetRelation']
  const getDataArchitectureDataProjectDomainEntityQueryTree: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainEntityQueryTree']
  const getDataArchitectureDataProjectDomainEntityVersionList: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainEntityVersionList']
  const getDataArchitectureDataProjectDomainQueryList: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainQueryList']
  const getDataArchitectureDataProjectDomainQueryTree: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectDomainQueryTree']
  const getDataArchitectureDataProjectList: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectList']
  const getDataArchitectureDataProjectListBusiness: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['getDataArchitectureDataProjectListBusiness']
  const getDataIntegrationMethodServerDownload: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['getDataIntegrationMethodServerDownload']
  const getDataMetaTaskSource: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['getDataMetaTaskSource']
  const getDataMetaTaskSourceByCollectId: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['getDataMetaTaskSourceByCollectId']
  const getDataMetaTaskTableColumns: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['getDataMetaTaskTableColumns']
  const getDataSourceGetDataSourceByType: typeof import('../src/api/modules/project/shujuyuanguanli')['getDataSourceGetDataSourceByType']
  const getDataSourceGetProjectDataSource: typeof import('../src/api/modules/project/shujuyuanguanli')['getDataSourceGetProjectDataSource']
  const getDataSourceGetTableById: typeof import('../src/api/modules/project/shujuyuanguanli')['getDataSourceGetTableById']
  const getDataSourceType: typeof import('../src/api/modules/project/shujuyuanguanli')['getDataSourceType']
  const getDataStandardDictionaryDictDownloadImportTemplate: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['getDataStandardDictionaryDictDownloadImportTemplate']
  const getDataStandardElementElementDownloadImportTemplate: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['getDataStandardElementElementDownloadImportTemplate']
  const getDataWarehouseLayerRules: typeof import('../src/api/modules/project/shucangfenceng')['getDataWarehouseLayerRules']
  const getDataWarehouseModelColumns: typeof import('../src/api/modules/project/shucangmoxing')['getDataWarehouseModelColumns']
  const getDataWarehouseModelDataSources: typeof import('../src/api/modules/project/shucangmoxing')['getDataWarehouseModelDataSources']
  const getDataWarehouseModelProjectDataSources: typeof import('../src/api/modules/project/shucangmoxing')['getDataWarehouseModelProjectDataSources']
  const getDataWarehouseModeldataDataSourceAll: typeof import('../src/api/modules/project/shucangshujuchaxun')['getDataWarehouseModeldataDataSourceAll']
  const getDataWorkflowFindDsDataSource: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['getDataWorkflowFindDsDataSource']
  const getDictItemList: typeof import('../src/api/modules/configData/zidianfuwu')['getDictItemList']
  const getDictItemListBatch: typeof import('../src/api/modules/configData/zidianfuwu')['getDictItemListBatch']
  const getGroupDetailById: typeof import('../src/api/modules/assist/qunzujiekou')['getGroupDetailById']
  const getInfoDbSources: typeof import('../src/api/modules/project/shujukuxinxi')['getInfoDbSources']
  const getInfoFlinkAddFlink: typeof import('../src/api/modules/project/flinkxinxi')['getInfoFlinkAddFlink']
  const getInfoFlinkDeletedFlink: typeof import('../src/api/modules/project/flinkxinxi')['getInfoFlinkDeletedFlink']
  const getInfoFlinkFindAll: typeof import('../src/api/modules/project/flinkxinxi')['getInfoFlinkFindAll']
  const getLogLoginDetailLogin: typeof import('../src/api/modules/log/denglurizhifuwu')['getLogLoginDetailLogin']
  const getLogOperateDetailOperate: typeof import('../src/api/modules/log/caozuorizhifuwu')['getLogOperateDetailOperate']
  const getMonitorAppList: typeof import('../src/api/modules/project/yingyongjiankong')['getMonitorAppList']
  const getMonitorEmailFindList: typeof import('../src/api/modules/project/jiankongbaojing')['getMonitorEmailFindList']
  const getNoticeDeleteNotice: typeof import('../src/api/modules/assist/gonggaofuwu')['getNoticeDeleteNotice']
  const getNoticeDeleteNoticeSend: typeof import('../src/api/modules/assist/gonggaofuwu')['getNoticeDeleteNoticeSend']
  const getNoticeDetailNotice: typeof import('../src/api/modules/assist/gonggaofuwu')['getNoticeDetailNotice']
  const getPlatAdmin: typeof import('../src/api/modules/project/pingtaitongji')['getPlatAdmin']
  const getPlatDay: typeof import('../src/api/modules/project/pingtaitongji')['getPlatDay']
  const getPlatGet: typeof import('../src/api/modules/project/pingtaitongji')['getPlatGet']
  const getRbacCollectByIdDetail: typeof import('../src/api/modules/rbac/yonghushoucangbiaojiekou')['getRbacCollectByIdDetail']
  const getRbacOrgDeleteOrg: typeof import('../src/api/modules/rbac/zuzhijiekou')['getRbacOrgDeleteOrg']
  const getRbacOrgInfo: typeof import('../src/api/modules/rbac/zuzhijiekou')['getRbacOrgInfo']
  const getRbacOrgOrgListByParentId: typeof import('../src/api/modules/rbac/zuzhijiekou')['getRbacOrgOrgListByParentId']
  const getRbacOrgRoleGetAllByOrgId: typeof import('../src/api/modules/rbac/zuzhijiaosebiaokongzhiqi')['getRbacOrgRoleGetAllByOrgId']
  const getRbacOrgRoleGetByOrgId: typeof import('../src/api/modules/rbac/zuzhijiaosebiaokongzhiqi')['getRbacOrgRoleGetByOrgId']
  const getRbacOrgSearchSubOrgById: typeof import('../src/api/modules/rbac/zuzhijiekou')['getRbacOrgSearchSubOrgById']
  const getRbacOrgSearchTreeById: typeof import('../src/api/modules/rbac/zuzhijiekou')['getRbacOrgSearchTreeById']
  const getRbacPostDetailById: typeof import('../src/api/modules/rbac/gangweijiekou')['getRbacPostDetailById']
  const getRbacPostList: typeof import('../src/api/modules/rbac/gangweijiekou')['getRbacPostList']
  const getRbacPubDataPermissionsDataPermissionList: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['getRbacPubDataPermissionsDataPermissionList']
  const getRbacPubDataPermissionsGetById: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['getRbacPubDataPermissionsGetById']
  const getRbacPubRoleGroupGetAll: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['getRbacPubRoleGroupGetAll']
  const getRbacPubRoleGroupGetById: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['getRbacPubRoleGroupGetById']
  const getRbacResourceAppList: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceAppList']
  const getRbacResourceGetRoleListByResourceId: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceGetRoleListByResourceId']
  const getRbacResourceGrantedResources: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceGrantedResources']
  const getRbacResourceGroupResourceTree: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceGroupResourceTree']
  const getRbacResourceInfo: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceInfo']
  const getRbacResourceResourceDelete: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceResourceDelete']
  const getRbacResourceResourceListByParentId: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceResourceListByParentId']
  const getRbacResourceResourceTree: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceResourceTree']
  const getRbacResourceTenantAddResourceTree: typeof import('../src/api/modules/rbac/ziyuanjiekou')['getRbacResourceTenantAddResourceTree']
  const getRbacRoleInfo: typeof import('../src/api/modules/rbac/jiaosejiekou')['getRbacRoleInfo']
  const getRbacRoleRoleDelete: typeof import('../src/api/modules/rbac/jiaosejiekou')['getRbacRoleRoleDelete']
  const getRbacRoleRoleList: typeof import('../src/api/modules/rbac/jiaosejiekou')['getRbacRoleRoleList']
  const getRbacTenantPubTenantDelete: typeof import('../src/api/modules/rbac/zuhuguanlijiekou')['getRbacTenantPubTenantDelete']
  const getRbacTenantPubTenantInfo: typeof import('../src/api/modules/rbac/zuhuguanlijiekou')['getRbacTenantPubTenantInfo']
  const getRbacUsedByIdDetail: typeof import('../src/api/modules/rbac/yonghuzuijinchangyongbiaojiekou')['getRbacUsedByIdDetail']
  const getRbacUserInfo: typeof import('../src/api/modules/rbac/yonghujiekou')['getRbacUserInfo']
  const getRbacUserUserDelete: typeof import('../src/api/modules/rbac/yonghujiekou')['getRbacUserUserDelete']
  const getRbacUserUserEntireInfo: typeof import('../src/api/modules/rbac/yonghujiekou')['getRbacUserUserEntireInfo']
  const getRegionDeleteRegionByCode: typeof import('../src/api/modules/assist/quyujiekou')['getRegionDeleteRegionByCode']
  const getRegionGetListByParentId: typeof import('../src/api/modules/assist/quyujiekou')['getRegionGetListByParentId']
  const getRegionGetRegionDetailByCode: typeof import('../src/api/modules/assist/quyujiekou')['getRegionGetRegionDetailByCode']
  const getSysconfigDownLoadImageStreamByCode: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['getSysconfigDownLoadImageStreamByCode']
  const getSysconfigGetSysSecurityConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['getSysconfigGetSysSecurityConfig']
  const getSysconfigGetSysThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['getSysconfigGetSysThemeStyleConfig']
  const getSysconfigGetThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['getSysconfigGetThemeStyleConfig']
  const getSysconfigGetUserThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['getSysconfigGetUserThemeStyleConfig']
  const getUserGetLoginLogTime: typeof import('../src/api/modules/project/userLogController')['getUserGetLoginLogTime']
  const getUserLastAppAccess: typeof import('../src/api/modules/project/userLogController')['getUserLastAppAccess']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const invalidateCache: typeof import('alova')['invalidateCache']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const it: typeof import('vitest')['it']
  const markRaw: typeof import('vue')['markRaw']
  const message: typeof import('ant-design-vue')['message']
  const nextTick: typeof import('vue')['nextTick']
  const notification: typeof import('ant-design-vue')['notification']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const postAccessList: typeof import('../src/api/modules/project/rizhiliebiao')['postAccessList']
  const postApiDataAppApiSqlTest: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataAppApiSqlTest']
  const postApiDataEdit: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataEdit']
  const postApiDataFindList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataFindList']
  const postApiDataGetApiList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataGetApiList']
  const postApiDataModelList: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataModelList']
  const postApiDataNodeUrlsByapiids: typeof import('../src/api/modules/project/apIguanlishujukaifangjiedian')['postApiDataNodeUrlsByapiids']
  const postApiDataRemove: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataRemove']
  const postApiDataSave: typeof import('../src/api/modules/project/apIguanlishujukaifang')['postApiDataSave']
  const postApiUserAddAuthorizationApi: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserAddAuthorizationApi']
  const postApiUserAddAuthorizationUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserAddAuthorizationUser']
  const postApiUserAddUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserAddUser']
  const postApiUserDeleteUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserDeleteUser']
  const postApiUserGetApiByProjectDomain: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserGetApiByProjectDomain']
  const postApiUserGetApiMsg: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserGetApiMsg']
  const postApiUserGetUserApiByProjectDomain: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserGetUserApiByProjectDomain']
  const postApiUserGetUserIsAuthorization: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserGetUserIsAuthorization']
  const postApiUserGetUserMsg: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserGetUserMsg']
  const postApiUserRevokeApi: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserRevokeApi']
  const postApiUserRevokeUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserRevokeUser']
  const postApiUserSetAdmin: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserSetAdmin']
  const postApiUserStartUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserStartUser']
  const postApiUserStopUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserStopUser']
  const postApiUserUpdateUser: typeof import('../src/api/modules/project/shujuquanxianzhanghaoguanliApi')['postApiUserUpdateUser']
  const postAppList: typeof import('../src/api/modules/project/rizhiliebiao')['postAppList']
  const postAppapiByhuor: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiByhuor']
  const postAppapiCallEfficiency: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiCallEfficiency']
  const postAppapiCalllog: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiCalllog']
  const postAppapiDailyReport: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiDailyReport']
  const postAppapiDetailList: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiDetailList']
  const postAppapiErrorLog: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postAppapiErrorLog']
  const postAssetcatalogQueueDeleted: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['postAssetcatalogQueueDeleted']
  const postAssetcatalogQueuePageQuery: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['postAssetcatalogQueuePageQuery']
  const postAssetcatalogQueueSave: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['postAssetcatalogQueueSave']
  const postAssetcatalogTableDataPageQuery: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['postAssetcatalogTableDataPageQuery']
  const postAssetcatalogTablePageQuery: typeof import('../src/api/modules/project/shujuzichanzichanmulu')['postAssetcatalogTablePageQuery']
  const postAssetinventoryAnalysis: typeof import('../src/api/modules/project/shujuzichanzichanpandian')['postAssetinventoryAnalysis']
  const postAssetinventoryDetail: typeof import('../src/api/modules/project/shujuzichanzichanpandian')['postAssetinventoryDetail']
  const postAssetinventoryQuality: typeof import('../src/api/modules/project/shujuzichanzichanpandian')['postAssetinventoryQuality']
  const postAssetinventoryTest: typeof import('../src/api/modules/project/shujuzichanzichanpandian')['postAssetinventoryTest']
  const postAuthChangeLoginOrg: typeof import('../src/api/modules/auth/renzhengfuwu')['postAuthChangeLoginOrg']
  const postAuthLogin: typeof import('../src/api/modules/auth/renzhengfuwu')['postAuthLogin']
  const postAuthLoginOut: typeof import('../src/api/modules/auth/renzhengfuwu')['postAuthLoginOut']
  const postAuthTokenRefresh: typeof import('../src/api/modules/auth/renzhengfuwu')['postAuthTokenRefresh']
  const postBaseUserPageQuery: typeof import('../src/api/modules/project/jichujiekou')['postBaseUserPageQuery']
  const postBlackWhiteListAddBlackWhiteList: typeof import('../src/api/modules/configData/heibaimingdanfuwu')['postBlackWhiteListAddBlackWhiteList']
  const postBlackWhiteListGetBlackWhiteListPageList: typeof import('../src/api/modules/configData/heibaimingdanfuwu')['postBlackWhiteListGetBlackWhiteListPageList']
  const postBlackWhiteListUpdateBlackWhiteList: typeof import('../src/api/modules/configData/heibaimingdanfuwu')['postBlackWhiteListUpdateBlackWhiteList']
  const postDataArchitectureDataAssetPermissionList: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataAssetPermissionList']
  const postDataArchitectureDataAssetPermissionSavePermission: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataAssetPermissionSavePermission']
  const postDataArchitectureDataProjectDeleted: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDeleted']
  const postDataArchitectureDataProjectDomainDeleted: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainDeleted']
  const postDataArchitectureDataProjectDomainEntityCancelTable: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityCancelTable']
  const postDataArchitectureDataProjectDomainEntityChange: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityChange']
  const postDataArchitectureDataProjectDomainEntityDeleted: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityDeleted']
  const postDataArchitectureDataProjectDomainEntityPageQuery: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityPageQuery']
  const postDataArchitectureDataProjectDomainEntityPageQueryTable: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityPageQueryTable']
  const postDataArchitectureDataProjectDomainEntitySave: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntitySave']
  const postDataArchitectureDataProjectDomainEntitySaveTable: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntitySaveTable']
  const postDataArchitectureDataProjectDomainEntityVersion: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainEntityVersion']
  const postDataArchitectureDataProjectDomainPageQuery: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainPageQuery']
  const postDataArchitectureDataProjectDomainSave: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectDomainSave']
  const postDataArchitectureDataProjectPageQuery: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectPageQuery']
  const postDataArchitectureDataProjectSave: typeof import('../src/api/modules/project/shujuzichanshujujiagou')['postDataArchitectureDataProjectSave']
  const postDataIntegrationMethodExcelAdd: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelAdd']
  const postDataIntegrationMethodExcelCreatePreview: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelCreatePreview']
  const postDataIntegrationMethodExcelDelete: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelDelete']
  const postDataIntegrationMethodExcelDetail: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelDetail']
  const postDataIntegrationMethodExcelEdit: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelEdit']
  const postDataIntegrationMethodExcelImportExcel: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelImportExcel']
  const postDataIntegrationMethodExcelPage: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelPage']
  const postDataIntegrationMethodExcelTemplate: typeof import('../src/api/modules/project/shujucaijiExcelshujujijiekou')['postDataIntegrationMethodExcelTemplate']
  const postDataIntegrationMethodRealTimeAdd: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeAdd']
  const postDataIntegrationMethodRealTimeCreatePreview: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeCreatePreview']
  const postDataIntegrationMethodRealTimeDelete: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeDelete']
  const postDataIntegrationMethodRealTimeDetail: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeDetail']
  const postDataIntegrationMethodRealTimeEdit: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeEdit']
  const postDataIntegrationMethodRealTimeMission: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimeMission']
  const postDataIntegrationMethodRealTimePage: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimePage']
  const postDataIntegrationMethodRealTimePageWorkflowInstance: typeof import('../src/api/modules/project/shujucaijishishicaiji')['postDataIntegrationMethodRealTimePageWorkflowInstance']
  const postDataIntegrationMethodServerAdd: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerAdd']
  const postDataIntegrationMethodServerCreatePreview: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerCreatePreview']
  const postDataIntegrationMethodServerDelete: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerDelete']
  const postDataIntegrationMethodServerDetail: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerDetail']
  const postDataIntegrationMethodServerEdit: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerEdit']
  const postDataIntegrationMethodServerExcelTemplate: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerExcelTemplate']
  const postDataIntegrationMethodServerMission: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerMission']
  const postDataIntegrationMethodServerPage: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerPage']
  const postDataIntegrationMethodServerTextTemplate: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerTextTemplate']
  const postDataIntegrationMethodServerUpdateClientSecret: typeof import('../src/api/modules/project/shujucaijijiekoucaiji')['postDataIntegrationMethodServerUpdateClientSecret']
  const postDataIntegrationMethodSqlAdd: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlAdd']
  const postDataIntegrationMethodSqlCreatePreview: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlCreatePreview']
  const postDataIntegrationMethodSqlDelete: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlDelete']
  const postDataIntegrationMethodSqlDetail: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlDetail']
  const postDataIntegrationMethodSqlEdit: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlEdit']
  const postDataIntegrationMethodSqlMission: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlMission']
  const postDataIntegrationMethodSqlPage: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlPage']
  const postDataIntegrationMethodSqlPageWorkflowInstance: typeof import('../src/api/modules/project/shujucaijiSqlcaiji')['postDataIntegrationMethodSqlPageWorkflowInstance']
  const postDataIntegrationMethodTableAdd: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableAdd']
  const postDataIntegrationMethodTableDelete: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableDelete']
  const postDataIntegrationMethodTableDetail: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableDetail']
  const postDataIntegrationMethodTableEdit: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableEdit']
  const postDataIntegrationMethodTableFindMapping: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableFindMapping']
  const postDataIntegrationMethodTableFindTargetMapping: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableFindTargetMapping']
  const postDataIntegrationMethodTableGetIncreaseType: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableGetIncreaseType']
  const postDataIntegrationMethodTableMission: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTableMission']
  const postDataIntegrationMethodTablePage: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTablePage']
  const postDataIntegrationMethodTablePageWorkflowInstance: typeof import('../src/api/modules/project/shujucaijitieyuancaiji')['postDataIntegrationMethodTablePageWorkflowInstance']
  const postDataMetaInstanceFindTaskRunLog: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaijishili')['postDataMetaInstanceFindTaskRunLog']
  const postDataMetaInstancePageWorkflowInstance: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaijishili')['postDataMetaInstancePageWorkflowInstance']
  const postDataMetaInstanceResult: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaijishili')['postDataMetaInstanceResult']
  const postDataMetaTableDel: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableDel']
  const postDataMetaTableDetail: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableDetail']
  const postDataMetaTableDownload: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableDownload']
  const postDataMetaTablePage: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTablePage']
  const postDataMetaTablePublish: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTablePublish']
  const postDataMetaTableSave: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableSave']
  const postDataMetaTableSetStatus: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableSetStatus']
  const postDataMetaTableVersions: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujuzhuce')['postDataMetaTableVersions']
  const postDataMetaTaskDel: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskDel']
  const postDataMetaTaskDetail: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskDetail']
  const postDataMetaTaskPage: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskPage']
  const postDataMetaTaskSave: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskSave']
  const postDataMetaTaskShutdown: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskShutdown']
  const postDataMetaTaskStartup: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskStartup']
  const postDataMetaTaskTable: typeof import('../src/api/modules/project/yuanshujuguanliyuanshujucaiji')['postDataMetaTaskTable']
  const postDataQualityRuleTemplateOptions: typeof import('../src/api/modules/project/shujuzhiliangguizemoban')['postDataQualityRuleTemplateOptions']
  const postDataQualityRuleTemplatePageList: typeof import('../src/api/modules/project/shujuzhiliangguizemoban')['postDataQualityRuleTemplatePageList']
  const postDataQualityTaskDel: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskDel']
  const postDataQualityTaskDetail: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskDetail']
  const postDataQualityTaskPageList: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskPageList']
  const postDataQualityTaskReport: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskReport']
  const postDataQualityTaskSave: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskSave']
  const postDataQualityTaskSaveDraft: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskSaveDraft']
  const postDataQualityTaskTablePublishOption: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskTablePublishOption']
  const postDataQualityTaskTaskLog: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskTaskLog']
  const postDataQualityTaskTaskLogPageList: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskTaskLogPageList']
  const postDataQualityTaskUpd: typeof import('../src/api/modules/project/renwurizhi')['postDataQualityTaskUpd']
  const postDataSecurityDesenTemplateBatchDel: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateBatchDel']
  const postDataSecurityDesenTemplateDel: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateDel']
  const postDataSecurityDesenTemplateDesenTest: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateDesenTest']
  const postDataSecurityDesenTemplateDetail: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateDetail']
  const postDataSecurityDesenTemplateOptions: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateOptions']
  const postDataSecurityDesenTemplatePageList: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplatePageList']
  const postDataSecurityDesenTemplateSave: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateSave']
  const postDataSecurityDesenTemplateUpd: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateUpd']
  const postDataSecurityDesenTemplateUpdStatus: typeof import('../src/api/modules/project/shujuanquantuominmoban')['postDataSecurityDesenTemplateUpdStatus']
  const postDataSecurityDynamicMaskRuleBatchDel: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRuleBatchDel']
  const postDataSecurityDynamicMaskRuleDel: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRuleDel']
  const postDataSecurityDynamicMaskRuleDetail: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRuleDetail']
  const postDataSecurityDynamicMaskRulePageList: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRulePageList']
  const postDataSecurityDynamicMaskRuleUpd: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRuleUpd']
  const postDataSecurityDynamicMaskRuleUpdStatus: typeof import('../src/api/modules/project/shujuanquandongtaituominguize')['postDataSecurityDynamicMaskRuleUpdStatus']
  const postDataSecurityEncryptionAlgorithmBatchDel: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmBatchDel']
  const postDataSecurityEncryptionAlgorithmDel: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmDel']
  const postDataSecurityEncryptionAlgorithmDetail: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmDetail']
  const postDataSecurityEncryptionAlgorithmEncryptionMethodOptions: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmEncryptionMethodOptions']
  const postDataSecurityEncryptionAlgorithmNewOptions: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmNewOptions']
  const postDataSecurityEncryptionAlgorithmOptions: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmOptions']
  const postDataSecurityEncryptionAlgorithmPageList: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmPageList']
  const postDataSecurityEncryptionAlgorithmSave: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmSave']
  const postDataSecurityEncryptionAlgorithmUpd: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmUpd']
  const postDataSecurityEncryptionAlgorithmUpdStatus: typeof import('../src/api/modules/project/shujuanquanjiamisuanfa')['postDataSecurityEncryptionAlgorithmUpdStatus']
  const postDataSecurityKeyBatchDel: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyBatchDel']
  const postDataSecurityKeyDel: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyDel']
  const postDataSecurityKeyDetail: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyDetail']
  const postDataSecurityKeyEncryptionMethodOptions: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyEncryptionMethodOptions']
  const postDataSecurityKeyGenKey: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyGenKey']
  const postDataSecurityKeyImportKey: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyImportKey']
  const postDataSecurityKeyKeyOptions: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyKeyOptions']
  const postDataSecurityKeyPageList: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyPageList']
  const postDataSecurityKeySave: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeySave']
  const postDataSecurityKeyUpd: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyUpd']
  const postDataSecurityKeyUpdStatus: typeof import('../src/api/modules/project/shujuanquanmiyaoguanli')['postDataSecurityKeyUpdStatus']
  const postDataSecurityScanTaskBatchDel: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskBatchDel']
  const postDataSecurityScanTaskDel: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskDel']
  const postDataSecurityScanTaskDetail: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskDetail']
  const postDataSecurityScanTaskDetectionResultAddDynamicMaskRule: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskDetectionResultAddDynamicMaskRule']
  const postDataSecurityScanTaskDetectionResultDataLimit10: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskDetectionResultDataLimit10']
  const postDataSecurityScanTaskDetectionResultPageList: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskDetectionResultPageList']
  const postDataSecurityScanTaskPageList: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskPageList']
  const postDataSecurityScanTaskSave: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskSave']
  const postDataSecurityScanTaskSaveDraft: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskSaveDraft']
  const postDataSecurityScanTaskTablePublishOption: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskTablePublishOption']
  const postDataSecurityScanTaskTaskLog: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskTaskLog']
  const postDataSecurityScanTaskUpd: typeof import('../src/api/modules/project/shujuanquanminganshujushibie')['postDataSecurityScanTaskUpd']
  const postDataSecuritySecurityClassBatchDel: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassBatchDel']
  const postDataSecuritySecurityClassDel: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassDel']
  const postDataSecuritySecurityClassOptions: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassOptions']
  const postDataSecuritySecurityClassPageList: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassPageList']
  const postDataSecuritySecurityClassSave: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassSave']
  const postDataSecuritySecurityClassUpd: typeof import('../src/api/modules/project/shujuanquananquanmiji')['postDataSecuritySecurityClassUpd']
  const postDataSecuritySensitiveLabelBatchDel: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelBatchDel']
  const postDataSecuritySensitiveLabelDel: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelDel']
  const postDataSecuritySensitiveLabelDetail: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelDetail']
  const postDataSecuritySensitiveLabelOptions: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelOptions']
  const postDataSecuritySensitiveLabelPageList: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelPageList']
  const postDataSecuritySensitiveLabelSave: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelSave']
  const postDataSecuritySensitiveLabelUpd: typeof import('../src/api/modules/project/shujuanquanminganbiaoqian')['postDataSecuritySensitiveLabelUpd']
  const postDataSourceAdd: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourceAdd']
  const postDataSourceConnect: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourceConnect']
  const postDataSourceDelete: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourceDelete']
  const postDataSourceDetail: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourceDetail']
  const postDataSourceEdit: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourceEdit']
  const postDataSourcePage: typeof import('../src/api/modules/project/shujuyuanguanli')['postDataSourcePage']
  const postDataStandardDictionaryDictBatchDel: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictBatchDel']
  const postDataStandardDictionaryDictDel: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictDel']
  const postDataStandardDictionaryDictDetail: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictDetail']
  const postDataStandardDictionaryDictDictStatusStat: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictDictStatusStat']
  const postDataStandardDictionaryDictDictTree: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictDictTree']
  const postDataStandardDictionaryDictDownload: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictDownload']
  const postDataStandardDictionaryDictImport: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictImport']
  const postDataStandardDictionaryDictPageList: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictPageList']
  const postDataStandardDictionaryDictSaveDraft: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictSaveDraft']
  const postDataStandardDictionaryDictSaveOrUpd: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictSaveOrUpd']
  const postDataStandardDictionaryDictUpdStatus: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDictUpdStatus']
  const postDataStandardDictionaryDirectoryDel: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDirectoryDel']
  const postDataStandardDictionaryDirectorySave: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDirectorySave']
  const postDataStandardDictionaryDirectoryTree: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDirectoryTree']
  const postDataStandardDictionaryDirectoryUpd: typeof import('../src/api/modules/project/shujubiaozhunzidianbiaoguanli')['postDataStandardDictionaryDirectoryUpd']
  const postDataStandardElementDirectoryDel: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementDirectoryDel']
  const postDataStandardElementDirectorySave: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementDirectorySave']
  const postDataStandardElementDirectoryTree: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementDirectoryTree']
  const postDataStandardElementDirectoryUpd: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementDirectoryUpd']
  const postDataStandardElementElementBatchDel: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementBatchDel']
  const postDataStandardElementElementDataTypeOption: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementDataTypeOption']
  const postDataStandardElementElementDefFieldMapping: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementDefFieldMapping']
  const postDataStandardElementElementDel: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementDel']
  const postDataStandardElementElementDetail: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementDetail']
  const postDataStandardElementElementDownload: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementDownload']
  const postDataStandardElementElementElementStatusStat: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementElementStatusStat']
  const postDataStandardElementElementElementTree: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementElementTree']
  const postDataStandardElementElementHistoryVersion: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementHistoryVersion']
  const postDataStandardElementElementHistoryVersionDetail: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementHistoryVersionDetail']
  const postDataStandardElementElementImport: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementImport']
  const postDataStandardElementElementList: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementList']
  const postDataStandardElementElementSave: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementSave']
  const postDataStandardElementElementSaveDraft: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementSaveDraft']
  const postDataStandardElementElementUpdStatus: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementUpdStatus']
  const postDataStandardElementElementUpdate: typeof import('../src/api/modules/project/shujubiaozhunshujuyuanguanli')['postDataStandardElementElementUpdate']
  const postDataStandardStandardFileBatchDel: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileBatchDel']
  const postDataStandardStandardFileBatchDownload: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileBatchDownload']
  const postDataStandardStandardFileDel: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileDel']
  const postDataStandardStandardFileFileCategoryOption: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileFileCategoryOption']
  const postDataStandardStandardFilePageList: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFilePageList']
  const postDataStandardStandardFileSave: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileSave']
  const postDataStandardStandardFileStatusStat: typeof import('../src/api/modules/project/shujubiaozhunbiaozhunwenjianguanli')['postDataStandardStandardFileStatusStat']
  const postDataWarehouseLayerAdd: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerAdd']
  const postDataWarehouseLayerAll: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerAll']
  const postDataWarehouseLayerDelete: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerDelete']
  const postDataWarehouseLayerRuleAdd: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerRuleAdd']
  const postDataWarehouseLayerRuleDelete: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerRuleDelete']
  const postDataWarehouseLayerRuleUpdate: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerRuleUpdate']
  const postDataWarehouseLayerRules: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerRules']
  const postDataWarehouseLayerUpdate: typeof import('../src/api/modules/project/shucangfenceng')['postDataWarehouseLayerUpdate']
  const postDataWarehouseModelAdd: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelAdd']
  const postDataWarehouseModelColumns: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelColumns']
  const postDataWarehouseModelCreateTask: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelCreateTask']
  const postDataWarehouseModelDelete: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelDelete']
  const postDataWarehouseModelDetail: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelDetail']
  const postDataWarehouseModelMissionList: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelMissionList']
  const postDataWarehouseModelMissionLog: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelMissionLog']
  const postDataWarehouseModelMissionRestart: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelMissionRestart']
  const postDataWarehouseModelPage: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelPage']
  const postDataWarehouseModelUpdate: typeof import('../src/api/modules/project/shucangmoxing')['postDataWarehouseModelUpdate']
  const postDataWarehouseModeldataAdhocPageCreate: typeof import('../src/api/modules/project/shucangshujuchaxun')['postDataWarehouseModeldataAdhocPageCreate']
  const postDataWarehouseModeldataAdhocPageResult: typeof import('../src/api/modules/project/shucangshujuchaxun')['postDataWarehouseModeldataAdhocPageResult']
  const postDataWarehouseModeldataMainDataPage: typeof import('../src/api/modules/project/shucangshujuchaxun')['postDataWarehouseModeldataMainDataPage']
  const postDataWorkflowEncryptTaskFind: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowEncryptTaskFind']
  const postDataWorkflowEncryptTaskSave: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowEncryptTaskSave']
  const postDataWorkflowFindAllWorkGroup: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowFindAllWorkGroup']
  const postDataWorkflowFindDsDataSource: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowFindDsDataSource']
  const postDataWorkflowFindOne: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowFindOne']
  const postDataWorkflowFindOrCreateDsProject: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowFindOrCreateDsProject']
  const postDataWorkflowFindTaskRunLog: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowFindTaskRunLog']
  const postDataWorkflowGenerateTaskCode: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowGenerateTaskCode']
  const postDataWorkflowIncrTaskFind: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowIncrTaskFind']
  const postDataWorkflowIncrTaskSave: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowIncrTaskSave']
  const postDataWorkflowInstancePage: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowInstancePage']
  const postDataWorkflowOffline: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowOffline']
  const postDataWorkflowOnline: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowOnline']
  const postDataWorkflowPage: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowPage']
  const postDataWorkflowSaveDefine: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowSaveDefine']
  const postDataWorkflowScheduleFind: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowScheduleFind']
  const postDataWorkflowScheduleSave: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowScheduleSave']
  const postDataWorkflowShutdown: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowShutdown']
  const postDataWorkflowStartup: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowStartup']
  const postDataWorkflowTaskInstancePage: typeof import('../src/api/modules/project/shujukaifagongzuoliu')['postDataWorkflowTaskInstancePage']
  const postDbGetAll: typeof import('../src/api/modules/logApi/info')['postDbGetAll']
  const postDictDataAllDictDataColumnType: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataAllDictDataColumnType']
  const postDictDataCheck: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataCheck']
  const postDictDataDel: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataDel']
  const postDictDataFindByDictCode: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataFindByDictCode']
  const postDictDataRefreshFlinkDb: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataRefreshFlinkDb']
  const postDictDataTest: typeof import('../src/api/modules/project/zidiancaozuo')['postDictDataTest']
  const postDictItemDeleteById: typeof import('../src/api/modules/configData/zidianfuwu')['postDictItemDeleteById']
  const postDictItemPage: typeof import('../src/api/modules/configData/zidianfuwu')['postDictItemPage']
  const postDictItemSave: typeof import('../src/api/modules/configData/zidianfuwu')['postDictItemSave']
  const postDictTypeDeleteById: typeof import('../src/api/modules/configData/zidianfuwu')['postDictTypeDeleteById']
  const postDictTypePage: typeof import('../src/api/modules/configData/zidianfuwu')['postDictTypePage']
  const postDictTypeSave: typeof import('../src/api/modules/configData/zidianfuwu')['postDictTypeSave']
  const postEsGetAll: typeof import('../src/api/modules/logApi/info')['postEsGetAll']
  const postFileUpload: typeof import('../src/api/modules/project/wenjian')['postFileUpload']
  const postFileUploadPhoto: typeof import('../src/api/modules/project/wenjian')['postFileUploadPhoto']
  const postFileUploadPhotoOpen: typeof import('../src/api/modules/project/wenjian')['postFileUploadPhotoOpen']
  const postFileUploadPhotos: typeof import('../src/api/modules/project/wenjian')['postFileUploadPhotos']
  const postGroupDeleteById: typeof import('../src/api/modules/assist/qunzujiekou')['postGroupDeleteById']
  const postGroupFindPage: typeof import('../src/api/modules/assist/qunzujiekou')['postGroupFindPage']
  const postGroupList: typeof import('../src/api/modules/assist/qunzujiekou')['postGroupList']
  const postGroupSaveOrUpdate: typeof import('../src/api/modules/assist/qunzujiekou')['postGroupSaveOrUpdate']
  const postInfoDbAdd: typeof import('../src/api/modules/project/shujukuxinxi')['postInfoDbAdd']
  const postInfoDbDel: typeof import('../src/api/modules/project/shujukuxinxi')['postInfoDbDel']
  const postInfoDbGet: typeof import('../src/api/modules/project/shujukuxinxi')['postInfoDbGet']
  const postInfoDbGetAll: typeof import('../src/api/modules/project/shujukuxinxi')['postInfoDbGetAll']
  const postInfoDbUpsert: typeof import('../src/api/modules/project/shujukuxinxi')['postInfoDbUpsert']
  const postInfoEsDel: typeof import('../src/api/modules/project/searchxinxi')['postInfoEsDel']
  const postInfoEsGet: typeof import('../src/api/modules/project/searchxinxi')['postInfoEsGet']
  const postInfoEsGetAll: typeof import('../src/api/modules/project/searchxinxi')['postInfoEsGetAll']
  const postInfoEsUpsert: typeof import('../src/api/modules/project/searchxinxi')['postInfoEsUpsert']
  const postInfoKafkaDel: typeof import('../src/api/modules/project/kafkaxinxi')['postInfoKafkaDel']
  const postInfoKafkaGet: typeof import('../src/api/modules/project/kafkaxinxi')['postInfoKafkaGet']
  const postInfoKafkaGetAll: typeof import('../src/api/modules/project/kafkaxinxi')['postInfoKafkaGetAll']
  const postInfoKafkaUpsert: typeof import('../src/api/modules/project/kafkaxinxi')['postInfoKafkaUpsert']
  const postInfoRedisDel: typeof import('../src/api/modules/project/redisxinxi')['postInfoRedisDel']
  const postInfoRedisGet: typeof import('../src/api/modules/project/redisxinxi')['postInfoRedisGet']
  const postInfoRedisGetAll: typeof import('../src/api/modules/project/redisxinxi')['postInfoRedisGetAll']
  const postInfoRedisUpsert: typeof import('../src/api/modules/project/redisxinxi')['postInfoRedisUpsert']
  const postInfoServerGet: typeof import('../src/api/modules/project/fuwuqixinxi')['postInfoServerGet']
  const postInfoServerGetAll: typeof import('../src/api/modules/project/fuwuqixinxi')['postInfoServerGetAll']
  const postKafkaGetAll: typeof import('../src/api/modules/logApi/info')['postKafkaGetAll']
  const postLogLoginPage: typeof import('../src/api/modules/log/denglurizhifuwu')['postLogLoginPage']
  const postLogOperatePage: typeof import('../src/api/modules/log/caozuorizhifuwu')['postLogOperatePage']
  const postLoggingAccessLog: typeof import('../src/api/modules/project/loggingController')['postLoggingAccessLog']
  const postLoggingAppLog: typeof import('../src/api/modules/project/loggingController')['postLoggingAppLog']
  const postLoggingSystemLog: typeof import('../src/api/modules/project/loggingController')['postLoggingSystemLog']
  const postLoginList: typeof import('../src/api/modules/project/rizhiliebiao')['postLoginList']
  const postMonitorEmailAdd: typeof import('../src/api/modules/project/jiankongbaojing')['postMonitorEmailAdd']
  const postMonitorEmailDeleteById: typeof import('../src/api/modules/project/jiankongbaojing')['postMonitorEmailDeleteById']
  const postMonitorEmailUpdate: typeof import('../src/api/modules/project/jiankongbaojing')['postMonitorEmailUpdate']
  const postNoticePageNotice: typeof import('../src/api/modules/assist/gonggaofuwu')['postNoticePageNotice']
  const postNoticeSaveOrUpdateNotice: typeof import('../src/api/modules/assist/gonggaofuwu')['postNoticeSaveOrUpdateNotice']
  const postNoticeSaveOrUpdateNoticeSend: typeof import('../src/api/modules/assist/gonggaofuwu')['postNoticeSaveOrUpdateNoticeSend']
  const postOperationList: typeof import('../src/api/modules/project/rizhiliebiao')['postOperationList']
  const postRbacCollectAdd: typeof import('../src/api/modules/rbac/yonghushoucangbiaojiekou')['postRbacCollectAdd']
  const postRbacCollectByIdDelete: typeof import('../src/api/modules/rbac/yonghushoucangbiaojiekou')['postRbacCollectByIdDelete']
  const postRbacCollectFindPage: typeof import('../src/api/modules/rbac/yonghushoucangbiaojiekou')['postRbacCollectFindPage']
  const postRbacCollectRemoveCollect: typeof import('../src/api/modules/rbac/yonghushoucangbiaojiekou')['postRbacCollectRemoveCollect']
  const postRbacOrgAddOrg: typeof import('../src/api/modules/rbac/zuzhijiekou')['postRbacOrgAddOrg']
  const postRbacOrgRoleRoleOrgPage: typeof import('../src/api/modules/rbac/zuzhijiaosebiaokongzhiqi')['postRbacOrgRoleRoleOrgPage']
  const postRbacOrgRoleSaveOrgRole: typeof import('../src/api/modules/rbac/zuzhijiaosebiaokongzhiqi')['postRbacOrgRoleSaveOrgRole']
  const postRbacOrgSearch: typeof import('../src/api/modules/rbac/zuzhijiekou')['postRbacOrgSearch']
  const postRbacOrgSearchOrgByIds: typeof import('../src/api/modules/rbac/zuzhijiekou')['postRbacOrgSearchOrgByIds']
  const postRbacOrgUpdateOrg: typeof import('../src/api/modules/rbac/zuzhijiekou')['postRbacOrgUpdateOrg']
  const postRbacPostDeleteById: typeof import('../src/api/modules/rbac/gangweijiekou')['postRbacPostDeleteById']
  const postRbacPostFindPage: typeof import('../src/api/modules/rbac/gangweijiekou')['postRbacPostFindPage']
  const postRbacPostSave: typeof import('../src/api/modules/rbac/gangweijiekou')['postRbacPostSave']
  const postRbacPubDataPermissionsGetUserOrgDataPermissionList: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsGetUserOrgDataPermissionList']
  const postRbacPubDataPermissionsGrantDataPermission: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsGrantDataPermission']
  const postRbacPubDataPermissionsGrantUserRoleAndDatPermission: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsGrantUserRoleAndDatPermission']
  const postRbacPubDataPermissionsPageQuery: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsPageQuery']
  const postRbacPubDataPermissionsSave: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsSave']
  const postRbacPubDataPermissionsUpdate: typeof import('../src/api/modules/rbac/shujuquanxianzhubiaokongzhiqi')['postRbacPubDataPermissionsUpdate']
  const postRbacPubRoleGroupPageQuery: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['postRbacPubRoleGroupPageQuery']
  const postRbacPubRoleGroupSave: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['postRbacPubRoleGroupSave']
  const postRbacPubRoleGroupUpdate: typeof import('../src/api/modules/rbac/pubJiaosezukongzhiqi')['postRbacPubRoleGroupUpdate']
  const postRbacResourceAddResource: typeof import('../src/api/modules/rbac/ziyuanjiekou')['postRbacResourceAddResource']
  const postRbacResourceMenuTree: typeof import('../src/api/modules/rbac/ziyuanjiekou')['postRbacResourceMenuTree']
  const postRbacResourceResourceUpdate: typeof import('../src/api/modules/rbac/ziyuanjiekou')['postRbacResourceResourceUpdate']
  const postRbacRoleAddRole: typeof import('../src/api/modules/rbac/jiaosejiekou')['postRbacRoleAddRole']
  const postRbacRoleRolePage: typeof import('../src/api/modules/rbac/jiaosejiekou')['postRbacRoleRolePage']
  const postRbacRoleRoleUpdate: typeof import('../src/api/modules/rbac/jiaosejiekou')['postRbacRoleRoleUpdate']
  const postRbacRoleRoleUserPage: typeof import('../src/api/modules/rbac/jiaosejiekou')['postRbacRoleRoleUserPage']
  const postRbacTenantPubTenantAdd: typeof import('../src/api/modules/rbac/zuhuguanlijiekou')['postRbacTenantPubTenantAdd']
  const postRbacTenantPubTenantPage: typeof import('../src/api/modules/rbac/zuhuguanlijiekou')['postRbacTenantPubTenantPage']
  const postRbacTenantPubTenantUpdate: typeof import('../src/api/modules/rbac/zuhuguanlijiekou')['postRbacTenantPubTenantUpdate']
  const postRbacUsedAdd: typeof import('../src/api/modules/rbac/yonghuzuijinchangyongbiaojiekou')['postRbacUsedAdd']
  const postRbacUsedAddBatch: typeof import('../src/api/modules/rbac/yonghuzuijinchangyongbiaojiekou')['postRbacUsedAddBatch']
  const postRbacUsedFindPage: typeof import('../src/api/modules/rbac/yonghuzuijinchangyongbiaojiekou')['postRbacUsedFindPage']
  const postRbacUserChangePassword: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserChangePassword']
  const postRbacUserChangePasswordWithVerifyCode: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserChangePasswordWithVerifyCode']
  const postRbacUserGrantUserRoleAndDatPermission: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserGrantUserRoleAndDatPermission']
  const postRbacUserPubUserAdd: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserPubUserAdd']
  const postRbacUserResetPassword: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserResetPassword']
  const postRbacUserUserAndOrgList: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserUserAndOrgList']
  const postRbacUserUserForMultiOrgPage: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserUserForMultiOrgPage']
  const postRbacUserUserPage: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserUserPage']
  const postRbacUserUserUpdate: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserUserUpdate']
  const postRbacUserUserUpdateV2: typeof import('../src/api/modules/rbac/yonghujiekou')['postRbacUserUserUpdateV2']
  const postRedisGetAll: typeof import('../src/api/modules/logApi/info')['postRedisGetAll']
  const postRegionAddRegion: typeof import('../src/api/modules/assist/quyujiekou')['postRegionAddRegion']
  const postRegionUpdateRegion: typeof import('../src/api/modules/assist/quyujiekou')['postRegionUpdateRegion']
  const postServerGetAll: typeof import('../src/api/modules/logApi/info')['postServerGetAll']
  const postSortAdd: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortAdd']
  const postSortDlt: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortDlt']
  const postSortEdit: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortEdit']
  const postSortFind: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortFind']
  const postSortList: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortList']
  const postSortPage: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortPage']
  const postSortSimplelist: typeof import('../src/api/modules/project/shujulianjieshujufenlei')['postSortSimplelist']
  const postStatsGetAppRankStats: typeof import('../src/api/modules/project/shujutongji')['postStatsGetAppRankStats']
  const postStatsGetAppStats: typeof import('../src/api/modules/project/shujutongji')['postStatsGetAppStats']
  const postStatsGetDataStats: typeof import('../src/api/modules/project/shujutongji')['postStatsGetDataStats']
  const postStatsGetOrgStats: typeof import('../src/api/modules/project/shujutongji')['postStatsGetOrgStats']
  const postStatsGetUserStats: typeof import('../src/api/modules/project/shujutongji')['postStatsGetUserStats']
  const postSysconfigAddOrEditUserThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrEditUserThemeStyleConfig']
  const postSysconfigAddOrEidtSysThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrEidtSysThemeStyleConfig']
  const postSysconfigAddOrUpdateBackgroundImage: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrUpdateBackgroundImage']
  const postSysconfigAddOrUpdateBackgroundSmallImage: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrUpdateBackgroundSmallImage']
  const postSysconfigAddOrUpdateLogImage: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrUpdateLogImage']
  const postSysconfigAddOrUpdateLoginConfigurationItem: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigAddOrUpdateLoginConfigurationItem']
  const postSysconfigEditSysSecurityConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigEditSysSecurityConfig']
  const postSysconfigGetLoginConfigurationItem: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigGetLoginConfigurationItem']
  const postSysconfigResetUserThemeStyleConfig: typeof import('../src/api/modules/configData/xitongpeizhifuwu')['postSysconfigResetUserThemeStyleConfig']
  const postSystemList: typeof import('../src/api/modules/project/rizhiliebiao')['postSystemList']
  const postUserapiByhuor: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiByhuor']
  const postUserapiCallEfficiency: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiCallEfficiency']
  const postUserapiCalllog: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiCalllog']
  const postUserapiDailyReport: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiDailyReport']
  const postUserapiDetailList: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiDetailList']
  const postUserapiErrorLog: typeof import('../src/api/modules/project/yingyongjiekourizhixinxi')['postUserapiErrorLog']
  const provide: typeof import('vue')['provide']
  const putRbacUserChangePassword: typeof import('../src/api/modules/rbac/yonghujiekou')['putRbacUserChangePassword']
  const putRbacUserResetPassword: typeof import('../src/api/modules/rbac/yonghujiekou')['putRbacUserResetPassword']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const registerGlobalSystemInfo: typeof import('../src/utils/composables/useGlobalSystem')['registerGlobalSystemInfo']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const suite: typeof import('vitest')['suite']
  const test: typeof import('vitest')['test']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAuth: typeof import('../src/utils/composables/useAuth')['default']
  const useBox: typeof import('../src/composables/log/index')['useBox']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useError: typeof import('../src/composables/log/index')['useError']
  const useGetComputedStyle: typeof import('../src/utils/composables/useGetComputedStyle')['useGetComputedStyle']
  const useGetSidebarActualWidth: typeof import('../src/utils/composables/useGetComputedStyle')['useGetSidebarActualWidth']
  const useGlobalProperties: typeof import('../src/utils/composables/useGlobalProperties')['default']
  const useI18n: typeof import('vue-i18n')['useI18n']
  const useId: typeof import('vue')['useId']
  const useInfo: typeof import('../src/composables/log/index')['useInfo']
  const useLink: typeof import('vue-router')['useLink']
  const useLottie: typeof import('../src/utils/composables/useLottie')['useLottie']
  const useMainPage: typeof import('../src/utils/composables/useMainPage')['default']
  const useMenu: typeof import('../src/utils/composables/useMenu')['default']
  const useModel: typeof import('vue')['useModel']
  const usePagination: typeof import('../src/utils/composables/usePagination')['default']
  const useRequest: typeof import('alova')['useRequest']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useStart: typeof import('../src/composables/log/index')['useStart']
  const useSuccess: typeof import('../src/composables/log/index')['useSuccess']
  const useTabbar: typeof import('../src/utils/composables/useTabbar')['default']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTheme: typeof import('../src/composables/theme/index')['useTheme']
  const useTimeago: typeof import('../src/utils/composables/useTimeago')['default']
  const useTitle: typeof import('../src/utils/composables/useTitle')['useTitle']
  const useViewTransition: typeof import('../src/utils/composables/useViewTransition')['default']
  const useWarn: typeof import('../src/composables/log/index')['useWarn']
  const useWatchRouter: typeof import('../src/utils/composables/useWatchRouter')['useWatchRouter']
  const useWatermark: typeof import('../src/utils/composables/useWatermark')['default']
  const vi: typeof import('vitest')['vi']
  const vitest: typeof import('vitest')['vitest']
  const watch: typeof import('vue')['watch']
  const watchDiffObject: typeof import('../src/composables/watcheDiff')['watchDiffObject']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
