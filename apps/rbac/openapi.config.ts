import { defineConfig } from '@pubinfo/openapi';
import { presetName } from '@pubinfo/preset-openapi';

// const baseUrl = 'http://172.16.30.40:9099';

// const baseUrl = 'http://172.16.30.43:9099'; // 王凯

// const baseUrl = 'http://192.168.16.18:9099';
// const baseUrl = 'http://122.228.196.50:6099';
// const baseUrl = 'http://115.223.57.34:6099'; // 测试环境
// const baseUrl = 'http://172.16.30.42:9099'; // 胡鉴
const baseUrl = 'http://172.16.30.40:9099'; // 周佳琪

export default defineConfig({
  enabled: true,

  presets: [
    presetName({
      functionName: 'camelCase',
    }),
  ],

  imports: { '@/api/request': [{ name: 'basic', as: 'request' }] },
  batch: [
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/auth`,
      output: './src/api/modules/auth',
    },
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/rbac`,
      output: './src/api/modules/rbac',
    },
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/assist`,
      output: './src/api/modules/assist',
    },
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/configData`,
      output: './src/api/modules/configData',
    },
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/log`,
      output: './src/api/modules/log',
    },
    {
      input: `${baseUrl}/data-center-backend/v3/api-docs/project`,
      output: './src/api/modules/project',
    },
  ],

  hooks: {
    customType(schema, namespace, defaultFn) {
      // `int64` 在前端会精度丢失，可在此处调整对应字段的 `TS类型`
      if (schema?.type === 'integer' && schema?.format === 'int64') {
        return 'number';
      }

      return defaultFn(schema, namespace);
    },
  },
});
